services:
  balance-tracker:
    build:
      context: .
      dockerfile: core/balance-tracker/Dockerfile
    container_name: ${ZONE_NAME}-balance-tracker
    environment:
      - LOCAL_STACK_ENDPOINT=${LOCAL_STACK_ENDPOINT}
      - DB_BASE=${DB_BASE}
      - DB_PORT=${CORE_DB_PORT}
      - ZONE_TYPE=${ZONE_TYPE}
    profiles: ["fin", "biz"]

  transaction-tracker:
    build:
      context: .
      dockerfile: core/transaction-tracker/Dockerfile
    container_name: ${ZONE_NAME}-transaction-tracker
    environment:
      - LOCAL_STACK_ENDPOINT=${LOCAL_STACK_ENDPOINT}
      - DB_BASE=${DB_BASE}
      - DB_PORT=${CORE_DB_PORT}
      - ZONE_TYPE=${ZONE_TYPE}
    profiles: ["fin", "biz"]

  invoke-core-tracker:
    build:
      context: .
      dockerfile: core/invoke-core-tracker/Dockerfile
    container_name: ${ZONE_NAME}-invoke-core-tracker
    environment:
      - LOCAL_STACK_ENDPOINT=${LOCAL_STACK_ENDPOINT}
      - DB_BASE=${DB_BASE}
      - DB_PORT=${CORE_DB_PORT}
      - ZONE_TYPE=${ZONE_TYPE}
    profiles: ["fin", "biz"]

  push-notification-tracker:
    build:
      context: .
      dockerfile: bpm/push-notification-tracker/Dockerfile
    container_name: ${ZONE_NAME}-push-notification-tracker
    environment:
      - LOCAL_STACK_ENDPOINT=${LOCAL_STACK_ENDPOINT}
      - DB_BASE=${DB_BASE}
      - DB_PORT=${BPM_DB_PORT}
      - ZONE_TYPE=${ZONE_TYPE}
    profiles: ["fin"]

  email-send-tracker:
    build:
      context: .
      dockerfile: bpm/email-send-tracker/Dockerfile
    container_name: ${ZONE_NAME}-email-send-tracker
    environment:
      - LOCAL_STACK_ENDPOINT=${LOCAL_STACK_ENDPOINT}
      - DB_BASE=${DB_BASE}
      - DB_PORT=${BPM_DB_PORT}
      - ZONE_TYPE=${ZONE_TYPE}
    profiles: ["biz"]