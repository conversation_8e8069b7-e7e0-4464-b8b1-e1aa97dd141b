package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.config;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.StringUtils;

@ConfigurationProperties(prefix = "bctracker.push-notification")
@AllArgsConstructor
@ToString
public class PushNotificationTrackerProperty {

    /** SNS のローカル環境への接続先。 */
    public final String snsLocalEndpoint;

    /** 移転イベント発生時の通知の制御。 */
    public final Transfer transfer;

    /** アカウント限度額変更イベント発生時の通知の制御。 */
    public final AccountLimit accountLimit;

    @AllArgsConstructor
    @ToString
    @EqualsAndHashCode
    public static class Transfer {

        public final boolean individualEnabled;

        public final boolean companyEnabled;
    }

    @AllArgsConstructor
    @ToString
    @EqualsAndHashCode
    public static class AccountLimit {

        public final boolean individualEnabled;

        public final boolean companyEnabled;
    }

    /**
     * AWS 環境の SNS に接続するかどうか。
     *
     * @return AWS 環境の SNS に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.snsLocalEndpoint) == false);
    }
}
