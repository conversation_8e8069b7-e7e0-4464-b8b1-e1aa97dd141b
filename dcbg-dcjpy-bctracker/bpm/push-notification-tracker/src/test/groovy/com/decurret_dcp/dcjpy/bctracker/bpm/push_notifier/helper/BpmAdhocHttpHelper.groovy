package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.hc.core5.http.NameValuePair
import org.apache.hc.client5.http.impl.classic.HttpClients
import org.apache.hc.client5.http.classic.methods.*
import org.apache.hc.core5.net.URIBuilder
import org.apache.hc.core5.http.ContentType
import org.apache.hc.core5.http.io.entity.StringEntity
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse
import org.apache.hc.core5.http.message.BasicNameValuePair
import org.apache.hc.core5.http.io.entity.EntityUtils

class BpmAdhocHttpHelper {

    static final CloseableHttpClient httpClient = HttpClients.createDefault()

    static ObjectMapper mapper = new ObjectMapper()

    private final int port

    BpmAdhocHttpHelper(int port) {
        this.port = port
    }

    public HttpGetBuilder httpGet(String path) {
        return new HttpGetBuilder(path)
    }

    public HttpPostBuilder httpPost(String path) {
        return new HttpPostBuilder(path)
    }

    public HttpPutBuilder httpPut(String path) {
        return new HttpPutBuilder(path)
    }

    public HttpDeleteBuilder httpDelete(String path) {
        return new HttpDeleteBuilder(path)
    }

    protected abstract class HttpBuilder<BUILDER extends HttpBuilder<BUILDER, HTTP_BASE>, HTTP_BASE extends HttpUriRequestBase> {

        private final String path

        private String authorization

        private String serviceId

        private final List<NameValuePair> valuePairs

        protected boolean form

        protected HttpBuilder(String path) {
            this.path = path
            this.authorization = null
            this.serviceId = null
            this.valuePairs = new ArrayList<>()
            this.form = false
        }

        public BUILDER addQuery(String key, Object value) {
            def pair = new BasicNameValuePair(key, value.toString())
            return (BUILDER) this.addQuery(pair)
        }

        public BUILDER addQuery(BasicNameValuePair... pairs) {
            for (def pair : pairs) {
                this.valuePairs.add(pair)
            }

            return (BUILDER) this
        }

        public BUILDER setToken(String authorization) {
            this.authorization = "Bearer ${authorization}"
            return (BUILDER) this
        }

        public BUILDER setServiceId(String serviceId) {
            this.serviceId = serviceId
            return (BUILDER) this
        }

        public BUILDER setForm() {
            this.form = true
            return (BUILDER) this
        }

        public abstract HTTP_BASE initHttpBase(URIBuilder builder)

        public HTTP_BASE build() {
            URIBuilder builder = initURIBuilder(this.path, this.valuePairs)
            def httpBase = this.initHttpBase(builder)
            if (this.form == false) {
                httpBase.setHeader("Content-Type", "application/json")
            } else {
                httpBase.setHeader("Content-Type", "application/x-www-form-urlencoded")
            }
            if (this.authorization != null) {
                httpBase.setHeader("Authorization", this.authorization)
            }
            if (this.serviceId != null) {
                httpBase.setHeader("X-DCJPY-SERVICE-ID", this.serviceId)
            }

            return (HTTP_BASE) httpBase
        }
    }

    private URIBuilder initURIBuilder(String path, List<NameValuePair> valuePairs) {
        def builder = new URIBuilder()
                .setPath(path)
                .setScheme("http")
                .setHost("localhost")
                .setPort(this.port)

        if (valuePairs.size() > 0) {
            builder.addParameters(valuePairs)
        }

        return builder
    }

    private class HttpGetBuilder extends HttpBuilder<HttpGetBuilder, HttpGet> {

        private HttpGetBuilder(String path) {
            super(path)
        }

        @Override
        HttpGet initHttpBase(URIBuilder builder) {
            return new HttpGet(builder.build())
        }
    }

    private class HttpPostBuilder extends HttpBuilder<HttpPostBuilder, HttpPost> {

        private String requestBody

        private HttpPostBuilder(String path) {
            super(path)
        }

        public HttpPostBuilder setBody(String requestBody) {
            this.requestBody = requestBody
            return this
        }

        @Override
        HttpPost initHttpBase(URIBuilder builder) {
            return new HttpPost(builder.build())
        }

        public HttpPost build() {
            HttpPost httpPost = (HttpPost) super.build()
            if (this.requestBody != null) {
                ContentType contentType = (this.form == false)
                        ? ContentType.APPLICATION_JSON : ContentType.APPLICATION_FORM_URLENCODED
                httpPost.setEntity(new StringEntity(this.requestBody, contentType))
            }

            return httpPost
        }
    }

    private class HttpPutBuilder extends HttpBuilder<HttpPutBuilder, HttpPut> {

        private String requestBody

        private HttpPutBuilder(String path) {
            super(path)
        }

        public HttpPutBuilder setBody(String requestBody) {
            this.requestBody = requestBody
            return this
        }

        @Override
        HttpPut initHttpBase(URIBuilder builder) {
            return new HttpPut(builder.build())
        }

        public HttpPut build() {
            def httpPut = (HttpPut) super.build()
            if (this.requestBody != null) {
                ContentType contentType = (this.form == false)
                        ? ContentType.APPLICATION_JSON : ContentType.APPLICATION_FORM_URLENCODED
                httpPut.setEntity(new StringEntity(this.requestBody, contentType))
            }

            return httpPut
        }
    }

    private class HttpDeleteBuilder extends HttpBuilder<HttpDeleteBuilder, HttpDelete> {

        private HttpDeleteBuilder(String path) {
            super(path)
        }

        @Override
        HttpDelete initHttpBase(URIBuilder builder) {
            return new HttpDelete(builder.build())
        }
    }

    static JsonNode toJson(CloseableHttpResponse response) {
        String str = EntityUtils.toString(response.getEntity())
        return mapper.readTree(str)
    }
}
