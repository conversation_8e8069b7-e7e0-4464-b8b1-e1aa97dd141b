package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application.for_no_sns

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TemplateKey
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.TransferEvent
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application.PushNotificationTracker
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository.DcUserNotificationRepository
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.BpmAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.PublishResponse
import software.amazon.awssdk.services.sns.model.SnsException
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class PushNotificationTrackerSpecForNoSns extends Specification {
    @SpringBean
    SnsClient snsClientMock = Mock()

    @Autowired
    PushNotificationTracker pushNotificationTracker

    @Autowired
    DcUserNotificationRepository dcUserNotificationRepository

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockitoBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {

        sql = BpmAdhocHelper.initAdhoc(registry)
        registry.add("bctracker.push-notification.transfer.individual-enabled", () -> "true")
        registry.add("bctracker.push-notification.transfer.company-enabled", () -> "true")
        registry.add("bctracker.push-notification.account-limit.individual-enabled", () -> "true")
        registry.add("bctracker.push-notification.account-limit.company-enabled", () -> "true")
    }

    def setupSpec() {
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        BpmAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }


    /**
     * TransferイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのindexValuesの値
     */
    private static JsonNode getTransferIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * TransferイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTransferNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * SyncBusinessZoneStatusイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return SyncBusinessZoneStatusイベントのindexValuesの値
     */
    private static JsonNode getSyncBusinessZoneStatusIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * SyncBusinessZoneStatus(BizZone口座開設)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getApplyingNonIndexValues() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId": "3001",
                "zoneName": "モックギンコウコウザ1",
                "accountStatus": [97,112,112,108,121,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * SyncBusinessZoneStatus(BizZone解約)イベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTerminatingNonIndexValues() {
        String nonIndexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId": "3001",
                "zoneName": "モックギンコウコウザ1",
                "accountStatus": [116,101,114,109,105,110,97,116,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * ModTokenLimitイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのindexValuesの値
     */
    private static JsonNode getModTokenLimitIndexValues() {
        String indexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * ModTokenLimitイベントのNonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのNonIndexValuesの値
     */
    private static JsonNode getModTokenLimitNonIndexValues() {
        String nonIndexValues = """
            {
                "limitUpdates": {"mint": true, "burn": true, "charge": true, "discharge": true, "transfer": true, "cumulative": {"total": true, "mint": true, "burn": true, "charge": true, "discharge": true, "transfer": true}},
                "limitValues": {"mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000, "cumulative": {"total": 700000, "mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000}},
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testAcceptable_処理対象外のイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_transferイベントでvalidatorIdがclient_entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String testData = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """

        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_SyncBusinessZoneStatus(Bizゾーン口座開設)イベントでvalidatorIdがclient_entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()

        String testData = """
            {
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "zoneId": "3001",
                "zoneName": "ゾーン1",
                "accountStatus": [97,112,112,108,121,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_SyncBusinessZoneStatus(Bizゾーン口座解約)イベントでvalidatorIdがclient_entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()

        String testData = """
            {
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "zoneId": "3001",
                "zoneName": "ゾーン1",
                "accountStatus": [116,101,114,109,105,110,97,116,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_ModTokenLimitイベントでvalidatorIdがclient_entityテーブルに存在しない場合"() {
        setup:
        String textData = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]
            }
        """
        JsonNode indexValues = objectMapper.readTree(textData)

        JsonNode nonIndexValues = getModTokenLimitNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_transferイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.TRANSFER
        result.keywordPairs().size() == 1
        result.keywordPairs().get(0).keyword.toString().equals("COMPLETED_AT")
        !result.keywordPairs().get(0).value.isEmpty()
    }

    def "testAcceptable_transferイベントが発生した場合_miscValue2が複数"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5,310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p6",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues =  objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.TRANSFER
        result.keywordPairs().size() == 1
        result.keywordPairs().get(0).keyword.toString().equals("COMPLETED_AT")
        !result.keywordPairs().get(0).value.isEmpty()
    }

    def "testAcceptable_transferイベントが発生した場合_miscValue2が4096文字"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String nonIndexValuesStr = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "${'a' * 4096}",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues =  objectMapper.readTree(nonIndexValuesStr)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.TRANSFER
        result.keywordPairs().size() == 1
        result.keywordPairs().get(0).keyword.toString().equals("COMPLETED_AT")
        !result.keywordPairs().get(0).value.isEmpty()
    }

    def "testAcceptable_transferイベントが発生した場合_Escrowアカウント_#testCase"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        String testData = """
            {
                "transferData": {
                    "transferType": [99,104,97,114,103,101 ,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "0",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": $fromAccountId,
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": $toAccountId,
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)

        where:
        testCase                                | fromAccountId                                                                                             | toAccountId
        "fromAccountIdがEscrowアカウントの場合" | "[54,49,98,98,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99]" | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99]"
        "toAccountIdがEscrowアカウントの場合"   | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[54,49,98,98,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99]"
    }

    def "testAcceptable_SyncBusinessZoneStatus(Bizゾーン口座開設)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()
        JsonNode nonIndexValues = getApplyingNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.BIZ_APPLYING
        result.keywordPairs().size() == 2
        result.keywordPairs().get(0).keyword.toString().equals("ZONE_NAME")
        !result.keywordPairs().get(0).value.isEmpty()
        result.keywordPairs().get(1).keyword.toString().equals("OPERATED_AT")
    }

    def "testAcceptable_SyncBusinessZoneStatus(Bizゾーン口座解約)イベントが発生した場合"() {
        setup:
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()
        JsonNode nonIndexValues = getTerminatingNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.BIZ_TERMINATING
        result.keywordPairs().size() == 2
        result.keywordPairs().get(0).keyword.toString().equals("ZONE_NAME")
        !result.keywordPairs().get(0).value.isEmpty()
        result.keywordPairs().get(1).keyword.toString().equals("OPERATED_AT")
    }

    def "testAcceptable_SyncBusinessZoneStatusイベントが発生し、想定外のアカウントステータスの場合"() {
        setup:
        JsonNode indexValues = getSyncBusinessZoneStatusIndexValues()
        String testData = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "zoneId": "3001",
                "zoneName": "ゾーン1",
                "accountStatus": [102,114,111,122,101,110,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("SyncBusinessZoneStatus")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        this.pushNotificationTracker.acceptable(testEvent)

        then:
        thrown(IllegalArgumentException)
    }

    def "testAcceptable_ModTokenLimitイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getModTokenLimitIndexValues()
        JsonNode nonIndexValues = getModTokenLimitNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.ACCOUNT_UPDATED
        result.keywordPairs().size() == 1
        result.keywordPairs().get(0).keyword.toString().equals("COMPLETED_AT")
        !Objects.isNull(result.keywordPairs().get(0).value)
    }

    def "testAcceptable_ModTokenLimitイベントで更新データが存在しない場合"() {
        setup:
        JsonNode indexValues = getModTokenLimitIndexValues()

        String testData = """
            {
                "limitUpdates": {"mint": false, "burn": false, "charge": false, "discharge": false, "transfer": false, "cumulative": {"total": false, "mint": false, "burn": false, "charge": false, "discharge": false, "transfer": false}},
                "limitValues": {"mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000, "cumulative": {"total": 700000, "mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000}},
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_ModTokenLimitイベントで更新データが1つ存在する場合"() {
        setup:
        JsonNode indexValues = getModTokenLimitIndexValues()

        String testData = """
            {
                "limitUpdates": {"mint": true, "burn": false, "charge": false, "discharge": false, "transfer": false, "cumulative": {"total": false, "mint": false, "burn": false, "charge": false, "discharge": false, "transfer": false}},
                "limitValues": {"mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000, "cumulative": {"total": 700000, "mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000}},
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.validatorId().getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        result.accountId().getValue().equals("60bOvB8L3VxYSCM5QWd1WpSNenGaGFbb")
        result.transactionHash().getValue().equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(result.blockTimestamp())
        result.messageKey() == TemplateKey.ACCOUNT_UPDATED
        result.keywordPairs().size() == 1
        result.keywordPairs().get(0).keyword.toString().equals("COMPLETED_AT")
        !Objects.isNull(result.keywordPairs().get(0).value)
    }

    def "testOnMessage_transferイベントが発生した場合_#testCase"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()

        String testData = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": $fromValidatorId,
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": $fromAccountId,
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)

        when:
        this.pushNotificationTracker.onMessage(emailSenderMessage)

        then:
        thrown(IllegalStateException)

        where:
        testCase                                                                        | fromAccountId                                                                                             | fromValidatorId
        "dc_accountテーブルに、accountIdが合致するレコードが存在しない場合"              | "[54,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]"                                      | "[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]"
        "dc_accountテーブルに、validatorIdが合致するレコードが存在しない場合"            | "[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]" | "[56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]"
        "dc_accountテーブルに、accountIdとvalidatorIdが合致するレコードが存在しない場合" | "[54,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]"                                      | "[56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]"

    }

    def "testOnMessage_transferイベントが発生しSNSの送信でエラーになった場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        // snsClientのMockを設定しないと、AwsSnsAdaptor#のresponse.messageId()がNullPointerExceptionとなる。
        // 逆に言うと、この設定を行っている場合、snsClient.publishが1回呼ばれている。
        1 * snsClientMock.publish(_) >> {
            throw SnsException.builder().build()
        }

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
        """)
    }

    def "testOnMessage_通知対象ユーザに紐付くDCユーザ利用端末のレコードが存在しない場合は通知されずワーニングメッセージが出力されること"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        TransferEvent transferEvent = TransferEvent.create(testEvent)
        PushNotificationMessage emailSenderMessage = PushNotificationMessage.create(transferEvent)
        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        sql.execute("""
            DELETE FROM dc_user_device;
        """)

        when:
        def result = this.pushNotificationTracker.onMessage(emailSenderMessage)
        def record = sql.rows("""
            SELECT * FROM dc_user_notification;
        """)

        then:
        result == true

        !Objects.isNull(record)
        record.size() == 1
        record.get(0).get("sign_in_id").equals("SID01AA-1001")
        !Objects.isNull(record.get(0).get("notification_id"))
        record.get(0).get("notification_type").equals("transfer")
        record.get(0).get("message").equals("DCJPY 移転が完了しました。\n移転完了日時 : 2020.06.22 15:37")
        record.get(0).get("notification_detail").toString().contains("transaction_hash")
        record.get(0).get("notification_detail").toString().contains("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        !Objects.isNull(record.get(0).get("published_at"))

        cleanup:
        sql.execute("""
            DELETE FROM dc_user_notification;
        """)
        sql.execute("""
            INSERT INTO dc_user_device(sign_in_id, push_token, os_type) VALUES ('SID01AA-1001', 'xxxxxxxxxxxxxx', 'android');
        """)
    }
}
