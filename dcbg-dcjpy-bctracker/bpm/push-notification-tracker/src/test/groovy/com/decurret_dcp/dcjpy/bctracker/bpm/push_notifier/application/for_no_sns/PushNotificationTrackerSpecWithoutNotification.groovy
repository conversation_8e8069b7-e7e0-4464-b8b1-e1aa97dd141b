package com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application.for_no_sns

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.ModTokenLimitEvent
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.application.PushNotificationTracker
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.repository.DcUserNotificationRepository
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.domain.value.PushNotificationMessage
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.BpmAdhocHelper
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.BpmAdhocHttpHelper
import com.decurret_dcp.dcjpy.bctracker.bpm.push_notifier.helper.UniCodeHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient
import org.apache.hc.client5.http.impl.classic.HttpClients
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.CreatePlatformApplicationRequest
import software.amazon.awssdk.services.sns.model.CreatePlatformApplicationResponse
import software.amazon.awssdk.services.sns.model.CreatePlatformEndpointRequest
import software.amazon.awssdk.services.sns.model.CreatePlatformEndpointResponse
import software.amazon.awssdk.services.sns.model.DeleteEndpointRequest
import software.amazon.awssdk.services.sns.model.PublishResponse
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest
class PushNotificationTrackerSpecWithoutNotification extends Specification {

    static final CloseableHttpClient httpClient = HttpClients.createDefault()

    @Autowired
    PushNotificationTracker pushNotificationTracker

    @Autowired
    DcUserNotificationRepository dcUserNotificationRepository

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockitoBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    BpmAdhocHttpHelper snsHttpHelper

    SnsClient snsClient = SnsClient.builder()
            .region(Region.AP_NORTHEAST_1)
            .endpointOverride(URI.create("http://localhost:" + BpmAdhocHelper.getLocalStackPort()))
            .build()

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {

        sql = BpmAdhocHelper.initAdhoc(registry)
        registry.add("bctracker.push-notification.transfer.individual-enabled", () -> "false")
        registry.add("bctracker.push-notification.transfer.company-enabled", () -> "false")
        registry.add("bctracker.push-notification.account-limit.individual-enabled", () -> "false")
        registry.add("bctracker.push-notification.account-limit.company-enabled", () -> "false")
    }

    def setupSpec() {
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        BpmAdhocHelper.cleanupSpec()
    }

    def setup() {
        snsHttpHelper = new BpmAdhocHttpHelper(BpmAdhocHelper.getLocalStackPort().toInteger())
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * TransferイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのindexValuesの値
     */
    private static JsonNode getTransferIndexValues() {
        String indexValues = """
            {
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * 個人ユーザ起因のTransferイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTransferIndividualNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,48,56,119,85,117,114,55,51,75,89,83,119,84,122,116,115,110,110,89,50,116,52,56,85,51,114,48,85,89,55],
                    "fromAccountId": [54,48,48,56,119,85,117,114,55,51,75,89,83,119,84,122,116,115,110,110,89,50,116,52,56,85,51,114,48,85,89,55],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,48,56,119,85,117,114,55,51,75,89,83,119,84,122,116,115,110,110,89,50,116,52,56,85,51,114,48,85,89,55],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * 法人ユーザ起因のTransferイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return BurnイベントのnonIndexValuesの値
     */
    private static JsonNode getTransferCompanyNonIndexValues() {
        String nonIndexValues = """
            {
                "transferData": {
                    "transferType": [116,114,97,110,115,102,101,114,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "zoneId": "3000",
                    "fromValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "toValidatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountBalance": "10000",
                    "toAccountBalance": "20000",
                    "businessZoneBalance": "0",
                    "bizZoneId": "0",
                    "sendAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                    "fromAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                    "fromAccountName": "テストアカウント1",
                    "toAccountId": [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],
                    "toAccountName": "テストアカウント2",
                    "amount": "100",
                    "miscValue1": [49,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    "miscValue2": "310j4aD2zyQQlKa8RvPZ0BAc6bw4q6p5",
                    "memo": "メモmemo"
                },
            "traceId" : [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    /**
     * 個人ユーザ起因のModTokenLimitイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのindexValuesの値
     */
    private static JsonNode getModTokenLimitIndividualIndexValues() {
        String indexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,48,56,119,85,117,114,55,51,75,89,83,119,84,122,116,115,110,110,89,50,116,52,56,85,51,114,48,85,89,55]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * 法人ユーザ起因のModTokenLimitイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのindexValuesの値
     */
    private static JsonNode getModTokenLimitCompanyIndexValues() {
        String indexValues = """
            {
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],
                "accountId":  [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * ModTokenLimitイベントのNonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return ModTokenLimitイベントのNonIndexValuesの値
     */
    private static JsonNode getModTokenLimitCompanyNonIndexValues() {
        String nonIndexValues = """
            {
                "limitUpdates": {"mint": true, "burn": true, "charge": true, "discharge": true, "transfer": true, "cumulative": {"total": true, "mint": true, "burn": true, "charge": true, "discharge": true, "transfer": true}},
                "limitValues": {"mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000, "cumulative": {"total": 700000, "mint": 100000, "burn": 110000, "charge": 120000, "discharge": 130000, "transfer": 140000}},
                "traceId": [50,48,48,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testOnMessage_個人ユーザ起因のtransferイベントが発生し、通知制御のプロパティがFalse(通知しない)場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferIndividualNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:

        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        result == null
    }

    def "testOnMessage_法人ユーザ起因のtransferイベントが発生し、通知制御のプロパティがFalse(通知しない)場合"() {
        setup:
        JsonNode indexValues = getTransferIndexValues()
        JsonNode nonIndexValues = getTransferCompanyNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Transfer")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:

        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        result == null
    }

    def "testOnMessage_個人ユーザ起因のmodTokenLimitイベントが発生し、通知制御のプロパティがFalse(通知しない)場合"() {
        setup:
        JsonNode indexValues = getModTokenLimitIndividualIndexValues()
        JsonNode nonIndexValues = getModTokenLimitCompanyNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ModTokenLimitEvent event = ModTokenLimitEvent.create(testEvent)
        PushNotificationMessage message = PushNotificationMessage.create(event)

        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        result == null
    }

    def "testOnMessage_法人ユーザ起因のmodTokenLimitイベントが発生し、通知制御のプロパティがFalse(通知しない)場合"() {
        setup:
        JsonNode indexValues = getModTokenLimitCompanyIndexValues()
        JsonNode nonIndexValues = getModTokenLimitCompanyNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("ModTokenLimit")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(1592807820))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        ModTokenLimitEvent event = ModTokenLimitEvent.create(testEvent)
        PushNotificationMessage message = PushNotificationMessage.create(event)

        PublishResponse publishResponse = GroovyMock()
        publishResponse.messageId() >> "001"

        when:
        def result = this.pushNotificationTracker.acceptable(testEvent)

        then:
        result == null
    }
}
