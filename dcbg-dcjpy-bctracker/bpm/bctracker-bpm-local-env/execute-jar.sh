#!/bin/bash

# ローカルIT 環境構築手順でBCTracker BPMを"手動"で起動するためのスクリプト
# 引数は2つ
# 1つ目は銀行名
# 2つ目はコンポーネント名（push-notification-tracker または email-send-tracker）

BASE=$(
  cd "$(dirname "$0")" || exit
  pwd
)

REPO_PATH="${REPO_PATH:-$BASE/../../}"

if [ $# -ne 2 ]; then
  echo "引数は2個でなければならない"
  exit 0
fi

BANK=$1

if [ "${BANK}" != "ganb" ] && [ "${BANK}" != "jp-bank" ]; then
  echo "引数は ganb または jp-bank でなければならない"
  exit 0
fi

TRACKER=$2
if [ "${TRACKER}" != "push-notification-tracker" ] && [ "${TRACKER}" != "email-send-tracker" ]; then
  echo "引数は push-notification-tracker または email-send-tracker でなければならない"
  exit 0
fi


case $TRACKER in
  "push-notification-tracker")
    ENV="bpmfin"
    ;;
  "email-send-tracker")
    ENV="bpmbiz"
    ;;
  *)
    echo "無効なコンポーネントです。"
    exit 1
    ;;
esac


# ビルド
cd "${REPO_PATH}" || exit

if [ "${TRACKER}" == "push-notification-tracker" ]; then
  ./gradlew :bpm:push-notification-tracker:clean :bpm:push-notification-tracker:build -x test
else
  ./gradlew :bpm:email-send-tracker:clean :bpm:email-send-tracker:build -x test
fi

cd "${BASE}" || exit

# 環境構築読込
source "${REPO_PATH}/docker/local/env/${TRACKER}/${ENV}/common.env"
source "${REPO_PATH}/docker/local/env/${TRACKER}/${ENV}/${BANK}.env"

# jarの実行
java -jar "${REPO_PATH}/bpm/${TRACKER}/build/libs/${TRACKER}-0.0.1-SNAPSHOT.jar"









