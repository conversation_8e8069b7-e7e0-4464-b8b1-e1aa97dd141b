
INSERT INTO dc_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-********0000', '個人ユーザ', 'individual', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0010', '口座管理者', 'account_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0011', 'ユーザ管理者', 'user_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0012', '業務担当者', 'operator', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0013', '業務承認者', 'reviewer', '0', CURRENT_TIMESTAMP);

INSERT INTO service_user_role (role_id, role_name, role_type, service_id, created_at)
VALUES ('********-0000-4000-0000-********0020', 'サービス管理者', 'service_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-************', 'ユーザ管理者', 'user_owner', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0022', '業務担当者', 'operator', '0', CURRENT_TIMESTAMP),
       ('********-0000-4000-0000-********0023', '業務承認者', 'reviewer', '0', CURRENT_TIMESTAMP);

INSERT INTO dc_user_role_authority (role_id, authority_key)
VALUES
    -- 個人ユーザ
    ('********-0000-4000-0000-********0000', 'view_my_user'),
    ('********-0000-4000-0000-********0000', 'update_my_user'),
    ('********-0000-4000-0000-********0000', 'update_my_user_custom_sign_in'),
    ('********-0000-4000-0000-********0000', 'view_my_account'),
    ('********-0000-4000-0000-********0000', 'update_my_account'),
    ('********-0000-4000-0000-********0000', 'view_bank'),
    ('********-0000-4000-0000-********0000', 'execute_mint'),
    ('********-0000-4000-0000-********0000', 'execute_burn'),
    ('********-0000-4000-0000-********0000', 'execute_transfer'),
    ('********-0000-4000-0000-********0000', 'execute_charge'),
    ('********-0000-4000-0000-********0000', 'execute_discharge'),
    ('********-0000-4000-0000-********0000', 'view_accountsignature'),
    ('********-0000-4000-0000-********0000', 'view_transaction'),
    ('********-0000-4000-0000-********0000', 'execute_my_account'),
    ('********-0000-4000-0000-********0000', 'view_information'),
    ('********-0000-4000-0000-********0000', 'execute_contract'),

    -- アカウント管理者
    ('********-0000-4000-0000-********0010', 'view_my_user'),
    ('********-0000-4000-0000-********0010', 'update_my_user'),
    ('********-0000-4000-0000-********0010', 'update_my_user_custom_sign_in'),
    ('********-0000-4000-0000-********0010', 'view_my_account'),
    ('********-0000-4000-0000-********0010', 'update_my_account'),
    ('********-0000-4000-0000-********0010', 'view_user'),
    ('********-0000-4000-0000-********0010', 'update_user'),
    ('********-0000-4000-0000-********0010', 'update_user_custom_sign_in'),
    ('********-0000-4000-0000-********0010', 'view_bank'),
    ('********-0000-4000-0000-********0010', 'view_role'),
    ('********-0000-4000-0000-********0010', 'update_role'),
    ('********-0000-4000-0000-********0010', 'review_mint'),
    ('********-0000-4000-0000-********0010', 'execute_mint'),
    ('********-0000-4000-0000-********0010', 'review_burn'),
    ('********-0000-4000-0000-********0010', 'execute_burn'),
    ('********-0000-4000-0000-********0010', 'review_transfer'),
    ('********-0000-4000-0000-********0010', 'execute_transfer'),
    ('********-0000-4000-0000-********0010', 'review_charge'),
    ('********-0000-4000-0000-********0010', 'execute_charge'),
    ('********-0000-4000-0000-********0010', 'review_discharge'),
    ('********-0000-4000-0000-********0010', 'execute_discharge'),
    ('********-0000-4000-0000-********0010', 'view_nft'),
    ('********-0000-4000-0000-********0010', 'view_accountsignature'),
    ('********-0000-4000-0000-********0010', 'view_transaction'),
    ('********-0000-4000-0000-********0010', 'review_my_account'),
    ('********-0000-4000-0000-********0010', 'execute_my_account'),
    ('********-0000-4000-0000-********0010', 'operate_approve_transfer'),
    ('********-0000-4000-0000-********0010', 'review_approve_transfer'),
    ('********-0000-4000-0000-********0010', 'view_information'),
    ('********-0000-4000-0000-********0010', 'view_filetask'),
    ('********-0000-4000-0000-********0010', 'view_order'),
    ('********-0000-4000-0000-********0010', 'execute_contract'),

    -- ユーザ管理者
    ('********-0000-4000-0000-********0011', 'view_my_user'),
    ('********-0000-4000-0000-********0011', 'update_my_user'),
    ('********-0000-4000-0000-********0011', 'view_user'),
    ('********-0000-4000-0000-********0011', 'update_user'),
    ('********-0000-4000-0000-********0011', 'update_user_custom_sign_in'),
    ('********-0000-4000-0000-********0011', 'view_bank'),
    ('********-0000-4000-0000-********0011', 'view_role'),
    ('********-0000-4000-0000-********0011', 'update_role'),

    -- 業務担当者
    ('********-0000-4000-0000-********0012', 'view_my_user'),
    ('********-0000-4000-0000-********0012', 'update_my_user'),
    ('********-0000-4000-0000-********0012', 'view_my_account'),
    ('********-0000-4000-0000-********0012', 'view_bank'),
    ('********-0000-4000-0000-********0012', 'operate_mint'),
    ('********-0000-4000-0000-********0012', 'operate_burn'),
    ('********-0000-4000-0000-********0012', 'operate_transfer'),
    ('********-0000-4000-0000-********0012', 'operate_charge'),
    ('********-0000-4000-0000-********0012', 'operate_discharge'),
    ('********-0000-4000-0000-********0012', 'view_accountsignature'),
    ('********-0000-4000-0000-********0012', 'view_transaction'),
    ('********-0000-4000-0000-********0012', 'operate_my_account'),
    ('********-0000-4000-0000-********0012', 'operate_approve_transfer'),
    ('********-0000-4000-0000-********0012', 'view_information'),
    ('********-0000-4000-0000-********0012', 'execute_contract'),

    -- 業務承認者
    ('********-0000-4000-0000-********0013', 'view_my_user'),
    ('********-0000-4000-0000-********0013', 'update_my_user'),
    ('********-0000-4000-0000-********0013', 'view_my_account'),
    ('********-0000-4000-0000-********0013', 'view_bank'),
    ('********-0000-4000-0000-********0013', 'review_mint'),
    ('********-0000-4000-0000-********0013', 'review_burn'),
    ('********-0000-4000-0000-********0013', 'review_transfer'),
    ('********-0000-4000-0000-********0013', 'review_charge'),
    ('********-0000-4000-0000-********0013', 'review_discharge'),
    ('********-0000-4000-0000-********0013', 'view_accountsignature'),
    ('********-0000-4000-0000-********0013', 'view_transaction'),
    ('********-0000-4000-0000-********0013', 'review_my_account'),
    ('********-0000-4000-0000-********0013', 'review_approve_transfer'),
    ('********-0000-4000-0000-********0013', 'view_information'),
    ('********-0000-4000-0000-********0013', 'view_order');

INSERT INTO service_user_role_authority (role_id, authority_key)
VALUES
    -- サービス管理者
    ('********-0000-4000-0000-********0020', 'view_my_user'),
    ('********-0000-4000-0000-********0020', 'update_my_user'),
    ('********-0000-4000-0000-********0020', 'update_my_user_custom_sign_in'),
    ('********-0000-4000-0000-********0020', 'view_service_account'),
    ('********-0000-4000-0000-********0020', 'update_service_account'),
    ('********-0000-4000-0000-********0020', 'view_user'),
    ('********-0000-4000-0000-********0020', 'update_user'),
    ('********-0000-4000-0000-********0020', 'update_user_custom_sign_in'),
    ('********-0000-4000-0000-********0020', 'view_holder_role'),
    ('********-0000-4000-0000-********0020', 'update_holder_role'),
    ('********-0000-4000-0000-********0020', 'view_service_role'),
    ('********-0000-4000-0000-********0020', 'update_service_role'),
    ('********-0000-4000-0000-********0020', 'execute_mint'),
    ('********-0000-4000-0000-********0020', 'execute_burn'),
    ('********-0000-4000-0000-********0020', 'view_nft'),
    ('********-0000-4000-0000-********0020', 'operate_nft'),
    ('********-0000-4000-0000-********0020', 'operate_nft_mint'),
    ('********-0000-4000-0000-********0020', 'execute_nft_mint'),
    ('********-0000-4000-0000-********0020', 'operate_nft_transfer'),
    ('********-0000-4000-0000-********0020', 'execute_nft_transfer'),
    ('********-0000-4000-0000-********0020', 'view_account'),
    ('********-0000-4000-0000-********0020', 'view_account_detail'),
    ('********-0000-4000-0000-********0020', 'view_transaction'),
    ('********-0000-4000-0000-********0020', 'operate_account'),
    ('********-0000-4000-0000-********0020', 'review_account'),
    ('********-0000-4000-0000-********0020', 'execute_account'),
    ('********-0000-4000-0000-********0020', 'operate_account_frozen'),
    ('********-0000-4000-0000-********0020', 'review_account_frozen'),
    ('********-0000-4000-0000-********0020', 'operate_account_activated'),
    ('********-0000-4000-0000-********0020', 'review_account_activated'),
    ('********-0000-4000-0000-********0020', 'operate_account_force_burned'),
    ('********-0000-4000-0000-********0020', 'review_account_force_burned'),
    ('********-0000-4000-0000-********0020', 'operate_account_force_terminated'),
    ('********-0000-4000-0000-********0020', 'review_account_force_terminated'),
    ('********-0000-4000-0000-********0020', 'operate_account_user'),
    ('********-0000-4000-0000-********0020', 'review_account_user'),
    ('********-0000-4000-0000-********0020', 'execute_account_user'),
    ('********-0000-4000-0000-********0020', 'operate_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0020', 'review_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0020', 'execute_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0020', 'operate_account_user_suspended'),
    ('********-0000-4000-0000-********0020', 'review_account_user_suspended'),
    ('********-0000-4000-0000-********0020', 'execute_account_user_suspended'),
    ('********-0000-4000-0000-********0020', 'operate_account_user_activated'),
    ('********-0000-4000-0000-********0020', 'review_account_user_activated'),
    ('********-0000-4000-0000-********0020', 'execute_account_user_activated'),
    ('********-0000-4000-0000-********0020', 'view_information'),
    ('********-0000-4000-0000-********0020', 'operate_information'),
    ('********-0000-4000-0000-********0020', 'review_information'),
    ('********-0000-4000-0000-********0020', 'view_appversion'),
    ('********-0000-4000-0000-********0020', 'update_appversion'),
    ('********-0000-4000-0000-********0020', 'view_filetask'),
    ('********-0000-4000-0000-********0020', 'view_order'),
    ('********-0000-4000-0000-********0020', 'execute_contract'),
    ('********-0000-4000-0000-********0020', 'operate_account_created'),
    ('********-0000-4000-0000-********0020', 'review_account_created'),
    ('********-0000-4000-0000-********0020', 'review_account_reauth'),
    ('********-0000-4000-0000-********0020', 'operate_account_reauth'),
    ('********-0000-4000-0000-********0020', 'view_service_operation_logs'),

    -- ユーザ管理者
    ('********-0000-4000-0000-************', 'view_my_user'),
    ('********-0000-4000-0000-************', 'update_my_user'),
    ('********-0000-4000-0000-************', 'view_service_account'),
    ('********-0000-4000-0000-************', 'view_user'),
    ('********-0000-4000-0000-************', 'update_user'),
    ('********-0000-4000-0000-************', 'update_user_custom_sign_in'),
    ('********-0000-4000-0000-************', 'view_service_role'),
    ('********-0000-4000-0000-************', 'view_service_operation_logs'),

    -- 業務担当者
    ('********-0000-4000-0000-********0022', 'view_my_user'),
    ('********-0000-4000-0000-********0022', 'update_my_user'),
    ('********-0000-4000-0000-********0022', 'view_service_account'),
    ('********-0000-4000-0000-********0022', 'execute_mint'),
    ('********-0000-4000-0000-********0022', 'execute_burn'),
    ('********-0000-4000-0000-********0022', 'view_nft'),
    ('********-0000-4000-0000-********0022', 'operate_nft'),
    ('********-0000-4000-0000-********0022', 'operate_nft_mint'),
    ('********-0000-4000-0000-********0022', 'operate_nft_transfer'),
    ('********-0000-4000-0000-********0022', 'view_account'),
    ('********-0000-4000-0000-********0022', 'view_account_detail'),
    ('********-0000-4000-0000-********0022', 'view_transaction'),
    ('********-0000-4000-0000-********0022', 'operate_account'),
    ('********-0000-4000-0000-********0022', 'execute_account'),
    ('********-0000-4000-0000-********0022', 'operate_account_frozen'),
    ('********-0000-4000-0000-********0022', 'operate_account_activated'),
    ('********-0000-4000-0000-********0022', 'operate_account_force_burned'),
    ('********-0000-4000-0000-********0022', 'operate_account_force_terminated'),
    ('********-0000-4000-0000-********0022', 'operate_account_user'),
    ('********-0000-4000-0000-********0022', 'execute_account_user'),
    ('********-0000-4000-0000-********0022', 'operate_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0022', 'execute_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0022', 'operate_account_user_suspended'),
    ('********-0000-4000-0000-********0022', 'execute_account_user_suspended'),
    ('********-0000-4000-0000-********0022', 'operate_account_user_activated'),
    ('********-0000-4000-0000-********0022', 'execute_account_user_activated'),
    ('********-0000-4000-0000-********0022', 'view_information'),
    ('********-0000-4000-0000-********0022', 'operate_information'),
    ('********-0000-4000-0000-********0022', 'view_filetask'),
    ('********-0000-4000-0000-********0022', 'execute_contract'),
    ('********-0000-4000-0000-********0022', 'operate_account_created'),
    ('********-0000-4000-0000-********0022', 'operate_account_reauth'),

    -- 業務承認者
    ('********-0000-4000-0000-********0023', 'view_my_user'),
    ('********-0000-4000-0000-********0023', 'update_my_user'),
    ('********-0000-4000-0000-********0023', 'view_service_account'),
    ('********-0000-4000-0000-********0023', 'view_account'),
    ('********-0000-4000-0000-********0023', 'view_account_detail'),
    ('********-0000-4000-0000-********0023', 'view_transaction'),
    ('********-0000-4000-0000-********0023', 'review_account'),
    ('********-0000-4000-0000-********0023', 'review_account_frozen'),
    ('********-0000-4000-0000-********0023', 'review_account_activated'),
    ('********-0000-4000-0000-********0023', 'review_account_force_burned'),
    ('********-0000-4000-0000-********0023', 'review_account_force_terminated'),
    ('********-0000-4000-0000-********0023', 'review_account_user'),
    ('********-0000-4000-0000-********0023', 'review_account_user_reset_authentication'),
    ('********-0000-4000-0000-********0023', 'review_account_user_suspended'),
    ('********-0000-4000-0000-********0023', 'review_account_user_activated'),
    ('********-0000-4000-0000-********0023', 'review_account_created'),
    ('********-0000-4000-0000-********0023', 'review_account_reauth'),
    ('********-0000-4000-0000-********0023', 'view_information'),
    ('********-0000-4000-0000-********0023', 'review_information'),
    ('********-0000-4000-0000-********0023', 'view_order');

INSERT INTO message_template(template_key, template_content)
VALUES ('order', e'承認依頼を受けました。\n依頼番号 : ${order_id}\n業務担当者 : ${order_user_name}\n依頼内容 : ${order_type}\n登録完了日時 : ${completed_at}'),
       ('order_completed', e'依頼が承認されました。\n依頼番号 : ${order_id}\n業務承認者 : ${reviewer_user_name}\n依頼内容 : ${order_type}\n登録完了日時 : ${completed_at}'),
       ('order_rejected', e'依頼が否認されました。\n依頼番号 : ${order_id}\n業務承認者 : ${reviewer_user_name}\n依頼内容 : ${order_type}\n否認理由分類 : ${reason_type}\n否認理由詳細 : ${reason_detail}\n登録完了日時 : ${completed_at}'),
       ('transfer', e'DCJPY 移転が完了しました。\n移転完了日時 : ${completed_at}'),
       ('account_updated', e'アカウント情報変更が完了しました。\nアカウント情報変更完了日時 : ${completed_at}'),
       ('sign_in', e'サインインが完了しました。'),
       ('biz_applying', e'アカウント開設受付が完了しました。\nアカウント開設を確定してください。\nビジネスゾーン名 : ${zone_name}\nアカウント開設受付完了日時 : ${operated_at}'),
       ('biz_terminating', e'アカウント解約受付が完了しました。\nアカウント解約を確定してください。\nビジネスゾーン名 : ${zone_name}\nアカウント解約受付完了日時 : ${operated_at}'),
       ('email_transfer', e'${transfer_at} に、お客さまから振込依頼を受け付けました。\nこのメールにお心当たりのない場合や、ご不明な点がある場合は、以下までお問い合わせください。\n${contact_email_address}\n本メールは送信専用のため、返信できません。');

INSERT INTO reason_code(reason_code, owner_type, operation_type, reason_title)
VALUES -- 個人/法人ユーザ アカウント解約
       ('UAST0003', 'user', 'account_terminated', '不正利用'),
       ('UAST0004', 'user', 'account_terminated', '本人死亡/法人廃業'),
       ('UAST0005', 'user', 'account_terminated', '相続/譲渡'),
       ('UAST0006', 'user', 'account_terminated', '自己都合'),
       ('UAST0007', 'user', 'account_terminated', 'その他'),
       -- 銀行ユーザ アカウント凍結
       ('SASF0003', 'service', 'account_frozen', '不正利用'),
       ('SASF0004', 'service', 'account_frozen', '本人死亡/法人廃業'),
       ('SASF0005', 'service', 'account_frozen', '相続/譲渡'),
       ('SASF0007', 'service', 'account_frozen', 'その他'),
       -- 銀行ユーザ アカウント凍結解除
       ('SASA0006', 'service', 'account_activated', '原因解消'),
       ('SASA0007', 'service', 'account_activated', 'その他'),
       -- 銀行ユーザ アカウント強制償却
       ('SASB0003', 'service', 'account_force_burned', '不正利用'),
       ('SASB0004', 'service', 'account_force_burned', '本人死亡/法人廃業'),
       ('SASB0005', 'service', 'account_force_burned', '相続/譲渡'),
       ('SASB0007', 'service', 'account_force_burned', 'その他'),
       -- 銀行ユーザ アカウント一部強制償却
       ('SASP0003', 'service', 'account_partial_force_burned', '不正利用'),
       ('SASP0004', 'service', 'account_partial_force_burned', '本人死亡/法人廃業'),
       ('SASP0005', 'service', 'account_partial_force_burned', '相続/譲渡'),
       ('SASP0007', 'service', 'account_partial_force_burned', 'その他'),
       -- 銀行ユーザ アカウント強制解約
       ('SAST0003', 'service', 'account_force_terminated', '不正利用'),
       ('SAST0004', 'service', 'account_force_terminated', '本人死亡/法人廃業'),
       ('SAST0005', 'service', 'account_force_terminated', '相続/譲渡'),
       ('SAST0007', 'service', 'account_force_terminated', 'その他'),
       -- 個人/法人ユーザ サインイン停止
       ('UUSS0001', 'user', 'user_suspended', '端末紛失'),
       ('UUSS0003', 'user', 'user_suspended', '不正利用'),
       ('UUSS0006', 'user', 'user_suspended', 'その他'),
       -- 個人/法人ユーザ ユーザ無効
       ('UUST0004', 'user', 'user_terminated', '使用予定無し'),
       ('UUST0006', 'user', 'user_terminated', 'その他'),
       -- 個人/法人ユーザ サインイン停止解除
       ('UUSA0005', 'user', 'user_activated', '原因解消'),
       ('UUSA0006', 'user', 'user_activated', 'その他'),
       -- 銀行/事業者ユーザ サインイン停止
       ('SUSS0001', 'service', 'user_suspended', '端末紛失'),
       ('SUSS0003', 'service', 'user_suspended', '不正利用'),
       ('SUSS0006', 'service', 'user_suspended', 'その他'),
       -- 銀行/事業者ユーザ ユーザ無効
       ('SUST0004', 'service', 'user_terminated', '使用予定無し'),
       ('SUST0006', 'service', 'user_terminated', 'その他'),
       -- 銀行/事業者ユーザ サインイン停止解除
       ('SUSA0005', 'service', 'user_activated', '原因解消'),
       ('SUSA0006', 'service', 'user_activated', 'その他'),
       -- 銀行/事業者ユーザ 法人ユーザ（アカウント管理者）のサインイン停止
       ('SAUS0001', 'service', 'account_owner_suspended', '端末紛失'),
       ('SAUS0003', 'service', 'account_owner_suspended', '不正利用'),
       ('SAUS0007', 'service', 'account_owner_suspended', 'その他'),
       -- 銀行/事業者ユーザ 法人ユーザ（アカウント管理者）のサインイン停止解除
       ('SAUA0006', 'service', 'account_owner_activated', '原因解消'),
       ('SAUA0007', 'service', 'account_owner_activated', 'その他'),

       -- 個人/法人ユーザ 承認依頼の否認理由
       -- DCJPY発行 承認
       ('UMOR0001', 'user', 'reject_mint', '金額誤り'),
       ('UMOR0006', 'user', 'reject_mint', 'その他'),
       -- DCJPY償却 承認
       ('UBOR0001', 'user', 'reject_burn', '金額誤り'),
       ('UBOR0006', 'user', 'reject_burn', 'その他'),
       -- DCJPY送金 承認/承認受付
       ('UTOR0001', 'user', 'reject_transfer', '金額誤り'),
       ('UTOR0002', 'user', 'reject_transfer', '移転先誤り'),
       ('UTOR0006', 'user', 'reject_transfer', 'その他'),
       -- DCJPYチャージ 承認
       ('UCOR0001', 'user', 'reject_charge', '金額誤り'),
       ('UCOR0002', 'user', 'reject_charge', 'チャージ先誤り'),
       ('UCOR0006', 'user', 'reject_charge', 'その他'),
       -- DCJPYディスチャージ 承認
       ('UDOR0001', 'user', 'reject_discharge', '金額誤り'),
       ('UDOR0006', 'user', 'reject_discharge', 'その他'),
       -- アカウント限度額変更 承認/承認受付
       ('UALR0001', 'user', 'reject_change_account_limit', '変更内容誤り'),
       ('UALR0006', 'user', 'reject_change_account_limit', 'その他'),
       -- DC口座表示名変更 承認/承認受付
       ('UANR0001', 'user', 'reject_change_account_name', '変更内容誤り'),
       ('UANR0006', 'user', 'reject_change_account_name', 'その他'),
       -- 移転許可設定 承認
       ('UATR0001','user','reject_approve_transfer','移転額誤り'),
       ('UATR0002','user','reject_approve_transfer','移転指示者誤り'),
       ('UATR0006','user','reject_approve_transfer','その他'),
       -- 精算条件設定 承認
       ('USSR0001', 'user', 'reject_change_account_settlement', '精算種別誤り'),
       ('USSR0002', 'user', 'reject_change_account_settlement', '指定日誤り'),
       ('USSR0006', 'user', 'reject_change_account_settlement', 'その他'),

       -- 銀行/事業者ユーザ 承認依頼の否認理由
       -- アカウント限度額変更 承認/承認受付
       ('SALR0001', 'service', 'reject_change_account_limit', '変更内容誤り'),
       ('SALR0002', 'service', 'reject_change_account_limit', '変更対象誤り'),
       ('SALR0006', 'service', 'reject_change_account_limit', 'その他'),
       -- DC口座表示名変更 承認/承認受付
       ('SANR0001', 'service', 'reject_change_account_name', '変更内容誤り'),
       ('SANR0002', 'service', 'reject_change_account_name', '変更対象誤り'),
       ('SANR0006', 'service', 'reject_change_account_name', 'その他'),
       -- アカウント 凍結 承認/承認受付
       ('SAFR0001', 'service', 'reject_account_frozen', '変更内容誤り'),
       ('SAFR0006', 'service', 'reject_account_frozen', 'その他'),
       -- アカウント 凍結解除 承認/承認受付
       ('SAAR0001', 'service', 'reject_account_activated', '変更内容誤り'),
       ('SAAR0006', 'service', 'reject_account_activated', 'その他'),
       -- アカウント 強制償却 承認/承認受付
       ('SABR0001', 'service', 'reject_account_force_burned', '変更内容誤り'),
       ('SABR0006', 'service', 'reject_account_force_burned', 'その他'),
       -- アカウント 一部強制償却 承認/承認受付
       ('SAPR0001', 'service', 'reject_account_partial_force_burned', '変更内容誤り'),
       ('SAPR0006', 'service', 'reject_account_partial_force_burned', 'その他'),
       -- アカウント 強制解約 承認/承認受付
       ('SATR0001', 'service', 'reject_account_force_terminated', '変更内容誤り'),
       ('SATR0006', 'service', 'reject_account_force_terminated', 'その他'),
       -- アカウント開設　承認/承認受付
       ('SACR0001', 'service', 'reject_account_created', '登録内容誤り'),
       ('SACR0006', 'service', 'reject_account_created', 'その他'),
       -- アカウント再認可　承認/承認受付
       ('SAER0001', 'service', 'reject_account_reauth', '変更内容誤り'),
       ('SAER0006', 'service', 'reject_account_reauth', 'その他'),
       -- ユーザ 情報変更 承認/承認受付
       ('SUNR0001', 'service', 'reject_change_user_name', '変更内容誤り'),
       ('SUNR0002', 'service', 'reject_change_user_name', '変更対象誤り'),
       ('SUNR0006', 'service', 'reject_change_user_name', 'その他'),
       -- ユーザ 認証リセット 承認/承認受付
       ('SARR0001', 'service', 'reject_reset_auth', '変更内容誤り'),
       ('SARR0006', 'service', 'reject_reset_auth', 'その他'),
       -- サインイン停止 承認/承認受付
       ('SUSR0001', 'service', 'reject_user_suspended', '変更内容誤り'),
       ('SUSR0006', 'service', 'reject_user_suspended', 'その他'),
       -- サインイン停止解除 承認/承認受付
       ('SUAR0001', 'service', 'reject_user_activated', '変更内容誤り'),
       ('SUAR0006', 'service', 'reject_user_activated', 'その他'),
       -- インフォメーション 編集 承認/承認受付
       ('SIER0001', 'service', 'reject_change_information', '変更内容誤り'),
       ('SIER0006', 'service', 'reject_change_information', 'その他'),

       -- 承認処理 システムエラー
       ('USYR0001', 'user', 'system_failure', 'エラー'),
       ('SSYR0001', 'service', 'system_failure', 'エラー');

INSERT INTO service_user_operation_master(screen_id, operation_name, request_url_pattern, request_method, screen_name, operation_conditions)
VALUES  ('b010100', '実行', '/services/sign_in', 'POST', 'サインイン', NULL),
        ('b010100', '初期表示', '/services/my_user', 'GET', 'サインイン', NULL),
        ('b010300', '仮パスワード変更実行', '/services/my_user/password', 'PUT', 'サインイン', NULL),
        ('b010300', '実行', '/services/sign_out', 'POST', 'サインアウト', NULL),
        ('b0C0000', '一覧照会', '/services/accounts', 'GET', 'アカウント照会', NULL),
        ('b0C0100', '詳細照会', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]', 'GET', 'アカウント照会', NULL),
        ('b040200', '詳細照会', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/all', 'GET', 'アカウント照会', NULL),
        ('b0C0300', '詳細照会', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]', 'GET', 'アカウント照会', NULL),
        ('bm0C0700', '限度額変更承認依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/limit/order', 'POST', 'アカウント情報変更', NULL),
        ('b0C0400', '初期表示', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/all', 'GET', 'アカウント取引履歴照会', NULL),
        ('b0C0400', '一覧照会', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/transactions', 'GET', 'アカウント取引履歴照会', NULL),
        ('b0C0500', '詳細照会', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]', 'GET', 'アカウント照会', NULL),
        ('bm0C0000', '初期表示', '/services/reason_codes', 'GET', 'アカウント強制償却', NULL),
        ('bm0C0000', 'アカウント全額強制償却確認', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/burned/check', 'POST', 'アカウント強制償却', NULL),
        ('bm0C0200', 'アカウント全額強制償却依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/burned/order', 'POST', 'アカウント強制償却', NULL),
        ('bm0C0100', '初期表示', '/services/reason_codes', 'GET', 'アカウント強制償却', NULL),
        ('bm0C0100', 'アカウント一部強制償却確認', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/partial_burned/check', 'POST', 'アカウント強制償却', NULL),
        ('bm0C0300', 'アカウント一部強制償却依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/partial_burned/order', 'POST', 'アカウント強制償却', NULL),
        ('bm040200', '初期表示', '/services/reason_codes', 'GET', 'アカウント強制解約', NULL),
        ('bm040200', '承認依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/terminated/order', 'POST', 'アカウント強制解約', NULL),
        ('bm040400', '初期表示', '/services/reason_codes', 'GET', 'アカウント凍結/凍結解除', NULL),
        ('bm040400', 'アカウント凍結依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/account_status/frozen/order', 'POST', 'アカウント凍結/凍結解除', NULL),
        ('bm040500', '初期表示', '/services/reason_codes', 'GET', 'アカウント凍結/凍結解除', NULL),
        ('bm040500', 'アカウント凍結解除依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/account_status/active/order', 'POST', 'アカウント凍結/凍結解除', NULL),
        ('bm0C0600', '法人ユーザ利用開始承認依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/reauth/order', 'POST', '法人ユーザ利用開始', NULL),
        ('bm0C0500', '法人ユーザ再認可承認依頼', '/services/accounts/order', 'POST', '法人ユーザ利用開始', NULL),
        ('bm040600', '承認依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/users/[a-zA-Z0-9._-]{8,16}/authentication/reset/order', 'POST', 'ユーザ認証情報リセット', NULL),
        ('bm040700', '初期表示', '/services/reason_codes', 'GET', 'ユーザサインイン停止/停止解除', NULL),
        ('bm040700', 'サインイン停止依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/users/[a-zA-Z0-9._-]{8,16}/user_status/suspended/order', 'POST', 'ユーザサインイン停止/停止解除', NULL),
        ('bm040800', '初期表示', '/services/reason_codes', 'GET', 'ユーザサインイン停止/停止解除', NULL),
        ('bm040800', 'サインイン停止解除依頼', '/services/accounts/DC[0-9A-Z]{3}-[0-9]{4}-[0-9]{4}-[0-9]/users/[a-zA-Z0-9._-]{8,16}/user_status/active/order', 'POST', 'ユーザサインイン停止/停止解除', NULL),
        ('b0D0000', '一覧照会', '/services/information', 'GET', 'インフォメーション照会', NULL),
        ('b050100', '詳細照会', '/services/information/.{1,36}', 'GET', 'インフォメーション照会', NULL),
        ('b050150', '編集承認依頼', '/services/information/.{1,36}/order', 'POST', 'インフォメーション編集', NULL),
        ('b050200', '新規作成承認依頼', '/services/information/order', 'POST', 'インフォメーション編集', NULL),
        ('b0E0000', '一覧照会', '/services/orders', 'GET', '承認依頼照会', NULL),
        ('b0E0000', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b0E0100', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060300', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060400', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b0E0200', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b0E0300', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060600', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060800', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060A00', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060B00', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060C00', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060D00', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b0E0500', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b0E0400', '詳細照会', '/services/orders/[0-9]+', 'GET', '承認依頼照会', NULL),
        ('b060300', '承認_アカウント凍結', '/services/orders/[0-9]+/account_status/frozen', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060400', '承認_アカウント凍結解除', '/services/orders/[0-9]+/account_status/active', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060600', '承認_アカウント強制解約', '/services/orders/[0-9]+/account_force_terminated', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060800', '承認_ユーザー認証情報リセット', '/services/orders/[0-9]+/auth_reset', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060A00', '承認_サインイン停止', '/services/orders/[0-9]+/user_status/suspended', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060B00', '承認_サインイン停止解除', '/services/orders/[0-9]+/user_status/active', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060C00', '承認_インフォメーション編集', '/services/orders/[0-9]+/information', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b060D00', '承認_インフォメーション新規作成', '/services/orders/[0-9]+/information', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b0E0200', '承認_アカウント全額強制償却', '/services/orders/[0-9]+/account_force_burned', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b0E0300', '承認_アカウント一部強制償却', '/services/orders/[0-9]+/account_partial_force_burn', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b0E0100', '承認_限度額変更', '/services/orders/[0-9]+/account/limit', 'POST', '承認依頼受諾/否認', '{"review_status": "approval"}'),
        ('b0E0100', '否認_初期表示', '/services/reason_codes', 'GET', '承認依頼受諾/否認', NULL),
        ('b0E0100', '否認_限度額変更', '/services/orders/[0-9]+/account/limit', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060300', '否認_アカウント凍結', '/services/orders/[0-9]+/account_status/frozen', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060400', '否認_アカウント凍結解除', '/services/orders/[0-9]+/account_status/active', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060600', '否認_アカウント強制解約', '/services/orders/[0-9]+/account_force_terminated', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060800', '否認_ユーザー認証情報リセット', '/services/orders/[0-9]+/auth_reset', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060A00', '否認_サインイン停止', '/services/orders/[0-9]+/user_status/suspended', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060B00', '否認_サインイン停止解除', '/services/orders/[0-9]+/user_status/active', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060C00', '否認_インフォメーション編集', '/services/orders/[0-9]+/information', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060D00', '否認_インフォメーション新規作成', '/services/orders/[0-9]+/information', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b060500', '否認_アカウント全額強制償却', '/services/orders/[0-9]+/account_force_burned', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b0E0300', '否認_アカウント一部強制償却', '/services/orders/[0-9]+/account_partial_force_burn', 'POST', '承認依頼受諾/否認', '{"review_status": "reject"}'),
        ('b0F0000', '一覧照会', '/services/users', 'GET', '銀行ユーザ照会', NULL),
        ('b0F0100', '詳細照会', '/services/users/[a-zA-Z0-9._-]{8,16}', 'GET', '銀行ユーザ照会', NULL),
        ('bm070000', '初期表示', '/services/service_roles', 'GET', '銀行ユーザ作成', NULL),
        ('bm070000', '実行', '/services/users', 'POST', '銀行ユーザ作成', NULL),
        ('b0B0000', 'ユーザー名変更実行', '/services/my_user', 'PUT', 'ユーザ情報変更', NULL),
        ('b0B0000', 'サインインID変更実行', '/services/my_user/custom_sign_in', 'PUT', 'ユーザ情報変更', NULL),
        ('b0F0100', 'ユーザー名変更実行', '/services/users/[a-zA-Z0-9._-]{8,16}', 'PUT', '銀行ユーザ情報変更', NULL),
        ('b0F0100', 'サインインID変更実行', '/services/users/[a-zA-Z0-9._-]{8,16}/custom_sign_in', 'PUT', '銀行ユーザ情報変更', NULL),
        ('bm070200', '初期表示', '/services/service_roles', 'GET', '銀行ユーザロール変更', NULL),
        ('bm070200', '実行', '/services/users/[a-zA-Z0-9._-]{8,16}/role', 'PUT', '銀行ユーザロール変更', NULL),
        ('bm070300', '実行', '/services/users/[a-zA-Z0-9._-]{8,16}/authentication/reset', 'PUT', '銀行ユーザ認証情報リセット', NULL),
        ('bm070500', '初期表示', '/services/reason_codes', 'GET', '銀行ユーザ状態変更', NULL),
        ('bm070500', 'サインイン停止実行', '/services/users/[a-zA-Z0-9._-]{8,16}/role', 'PUT', '銀行ユーザ状態変更', NULL),
        ('bm070600', '初期表示', '/services/reason_codes', 'GET', '銀行ユーザ状態変更', NULL),
        ('bm070600', '無効化実行', '/services/users/[a-zA-Z0-9._-]{8,16}/role', 'PUT', '銀行ユーザ状態変更', NULL),
        ('bm070700', '初期表示', '/services/reason_codes', 'GET', '銀行ユーザ状態変更', NULL),
        ('bm070700', 'サインイン停止解除実行', '/services/users/[a-zA-Z0-9._-]{8,16}/role', 'PUT', '銀行ユーザ状態変更', NULL),
        ('b080000', '一覧照会_銀行', '/services/service_roles', 'GET', '銀行ユーザ権限マスター管理', NULL),
        ('b080000', '一覧照会_法人', '/services/holder_roles', 'GET', '銀行ユーザ権限マスター管理', NULL),
        ('b080100', '詳細照会_銀行', '/services/service_roles/.{1,36}', 'GET', '銀行ユーザ権限マスター管理', NULL),
        ('b080100', '詳細照会_法人', '/services/holder_roles/.{1,36}', 'GET', '銀行ユーザ権限マスター管理', NULL),
        ('bm080000', '新規作成実行_法人', '/services/holder_roles', 'POST', '銀行ユーザ権限マスター管理', NULL),
        ('bm080100', '変更実行_法人', '/services/holder_roles/.{1,36}', 'PUT', '銀行ユーザ権限マスター管理', NULL),
        ('bm080002', '新規作成実行_銀行', '/services/service_roles', 'POST', '銀行ユーザ権限マスター管理', NULL),
        ('bm080102', '変更実行_銀行', '/services/service_roles/.{1,36}', 'PUT', '銀行ユーザ権限マスター管理', NULL),
        ('b110000', '一覧照会', '/services/service_operation_logs', 'GET', '銀行ユーザ操作ログ閲覧', NULL),
        ('b110000', 'ダウンロード', '/services/service_operation_logs/download', 'GET', '銀行ユーザ操作ログ閲覧', NULL),
        ('b100000', '一覧照会', '/services/app_versions', 'GET', 'アプリケーションバージョン管理', NULL),
        ('b100050', '変更実行', '/services/app_versions', 'PUT', 'アプリケーションバージョン管理', NULL),
        ('b0B0000', '実行', '/services/sign_out', 'POST', 'サインアウト', NULL),
        ('bm020000', '変更実行', '/services/my_user/password', 'PUT', 'サインインパスワード変更', NULL),
        ('bm020000', '実行', '/services/sign_out', 'POST', 'サインアウト', NULL),
        ('b030100', '実行', '/services/sign_out', 'POST', 'サインアウト', NULL);