package com.decurret_dcp.dcjpy.bctracker.bpm.email_sender.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.ToString;

@ConfigurationProperties(prefix = "bctracker.email")
@AllArgsConstructor
@ToString
public class EMailSendTrackerProperty {

    /** SES のローカル環境への接続先。 */
    public final String sesLocalEndpoint;

    /** 送信元のメールアドレス */
    public final String sourceEmailAddress;

    /** 問い合わせ先のメールアドレス */
    public final String contactEmailAddress;

    /**
     * AWS 環境の SNS に接続するかどうか。
     *
     * @return AWS 環境の SNS に接続する場合 true
     */
    public boolean toAwsConnection() {
        return (StringUtils.hasText(this.sesLocalEndpoint) == false);
    }
}
