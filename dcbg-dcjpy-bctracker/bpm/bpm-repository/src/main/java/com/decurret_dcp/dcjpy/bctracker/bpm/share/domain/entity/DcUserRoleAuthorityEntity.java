/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationAuthorityKey;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RoleId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザロール権限 : 個人 / 法人ユーザのロールごとの操作権限を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_role_authority")
public class DcUserRoleAuthorityEntity {

    /** ロールID. */
    @Id
    @Column(name = "role_id")
    public final RoleId roleId;

    /** 権限名. */
    @Id
    @Column(name = "authority_key")
    public final OperationAuthorityKey authorityKey;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param roleId ロールID
     * @param authorityKey 権限名
     */
    DcUserRoleAuthorityEntity(
        RoleId roleId,
        OperationAuthorityKey authorityKey
    ) {
        this.roleId = roleId;
        this.authorityKey = authorityKey;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserRoleAuthorityEntity オブジェクト
     */
    protected DcUserRoleAuthorityEntity(DcUserRoleAuthorityEntity org) {
        this.roleId = org.roleId;
        this.authorityKey = org.authorityKey;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserRoleAuthorityEntity [")
                .append("roleId=").append(this.roleId).append(", ")
                .append("authorityKey=").append(this.authorityKey)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.roleId, 
                this.authorityKey
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserRoleAuthorityEntity other = (DcUserRoleAuthorityEntity) obj;
        return true
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.authorityKey, other.authorityKey)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserRoleAuthorityEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** ロールID. */
        private RoleId roleId;

        /** 権限名. */
        private OperationAuthorityKey authorityKey;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserRoleAuthorityEntity object
         */
        public DcUserRoleAuthorityEntity build() {
            return new DcUserRoleAuthorityEntity(
                    this.roleId, 
                    this.authorityKey
            );
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set authorityKey.
         *
         * @param authorityKey 権限名
         * @return this builder
         */
        public Builder authorityKey(OperationAuthorityKey authorityKey) {
            this.authorityKey = authorityKey;
            return this;
        }
    }
}
// @formatter:on
