/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザ操作マスタテーブル : サービスユーザが操作する画面、操作内容、絞り込み条件を管理するマスタ.
 */
@Entity(immutable = true)
@Table(name = "service_user_operation_master")
public class ServiceUserOperationMasterEntity {

    /** 操作画面ID : 操作画面ID. */
    @Id
    @Column(name = "screen_id")
    public final String screenId;

    /** 操作内容. */
    @Id
    @Column(name = "operation_name")
    public final String operationName;

    /** リクエストURLパターン : URL. */
    @Column(name = "request_url_pattern")
    public final String requestUrlPattern;

    /** リクエストメソッド. */
    @Column(name = "request_method")
    public final String requestMethod;

    /** screen_name. */
    @Column(name = "screen_name")
    public final String screenName;

    /** 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}. */
    @Column(name = "operation_conditions")
    public final String operationConditions;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param screenId 操作画面ID : 操作画面ID
     * @param operationName 操作内容
     * @param requestUrlPattern リクエストURLパターン : URL
     * @param requestMethod リクエストメソッド
     * @param screenName screen_name
     * @param operationConditions 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}
     */
    ServiceUserOperationMasterEntity(
        String screenId,
        String operationName,
        String requestUrlPattern,
        String requestMethod,
        String screenName,
        String operationConditions
    ) {
        this.screenId = screenId;
        this.operationName = operationName;
        this.requestUrlPattern = requestUrlPattern;
        this.requestMethod = requestMethod;
        this.screenName = screenName;
        this.operationConditions = operationConditions;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserOperationMasterEntity オブジェクト
     */
    protected ServiceUserOperationMasterEntity(ServiceUserOperationMasterEntity org) {
        this.screenId = org.screenId;
        this.operationName = org.operationName;
        this.requestUrlPattern = org.requestUrlPattern;
        this.requestMethod = org.requestMethod;
        this.screenName = org.screenName;
        this.operationConditions = org.operationConditions;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserOperationMasterEntity [")
                .append("screenId=").append(this.screenId).append(", ")
                .append("operationName=").append(this.operationName).append(", ")
                .append("requestUrlPattern=").append(this.requestUrlPattern).append(", ")
                .append("requestMethod=").append(this.requestMethod).append(", ")
                .append("screenName=").append(this.screenName).append(", ")
                .append("operationConditions=").append(this.operationConditions)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.screenId, 
                this.operationName, 
                this.requestUrlPattern, 
                this.requestMethod, 
                this.screenName, 
                this.operationConditions
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserOperationMasterEntity other = (ServiceUserOperationMasterEntity) obj;
        return true
                && Objects.equals(this.screenId, other.screenId)
                && Objects.equals(this.operationName, other.operationName)
                && Objects.equals(this.requestUrlPattern, other.requestUrlPattern)
                && Objects.equals(this.requestMethod, other.requestMethod)
                && Objects.equals(this.screenName, other.screenName)
                && Objects.equals(this.operationConditions, other.operationConditions)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserOperationMasterEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 操作画面ID : 操作画面ID. */
        private String screenId;

        /** 操作内容. */
        private String operationName;

        /** リクエストURLパターン : URL. */
        private String requestUrlPattern;

        /** リクエストメソッド. */
        private String requestMethod;

        /** screen_name. */
        private String screenName;

        /** 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}. */
        private String operationConditions;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserOperationMasterEntity object
         */
        public ServiceUserOperationMasterEntity build() {
            return new ServiceUserOperationMasterEntity(
                    this.screenId, 
                    this.operationName, 
                    this.requestUrlPattern, 
                    this.requestMethod, 
                    this.screenName, 
                    this.operationConditions
            );
        }

        /**
         * Set screenId.
         *
         * @param screenId 操作画面ID : 操作画面ID
         * @return this builder
         */
        public Builder screenId(String screenId) {
            this.screenId = screenId;
            return this;
        }

        /**
         * Set operationName.
         *
         * @param operationName 操作内容
         * @return this builder
         */
        public Builder operationName(String operationName) {
            this.operationName = operationName;
            return this;
        }

        /**
         * Set requestUrlPattern.
         *
         * @param requestUrlPattern リクエストURLパターン : URL
         * @return this builder
         */
        public Builder requestUrlPattern(String requestUrlPattern) {
            this.requestUrlPattern = requestUrlPattern;
            return this;
        }

        /**
         * Set requestMethod.
         *
         * @param requestMethod リクエストメソッド
         * @return this builder
         */
        public Builder requestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
            return this;
        }

        /**
         * Set screenName.
         *
         * @param screenName screen_name
         * @return this builder
         */
        public Builder screenName(String screenName) {
            this.screenName = screenName;
            return this;
        }

        /**
         * Set operationConditions.
         *
         * @param operationConditions 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}
         * @return this builder
         */
        public Builder operationConditions(String operationConditions) {
            this.operationConditions = operationConditions;
            return this;
        }
    }
}
// @formatter:on
