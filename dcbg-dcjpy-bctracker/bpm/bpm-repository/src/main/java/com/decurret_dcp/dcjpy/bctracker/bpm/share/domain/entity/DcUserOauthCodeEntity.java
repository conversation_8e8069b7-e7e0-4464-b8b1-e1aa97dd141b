/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザOAuth認可コード : FinZone にて、Signer が BizZone 側に返却した認可コードを管理する.
 */
@Entity(immutable = true)
@Table(name = "dc_user_oauth_code")
public class DcUserOauthCodeEntity {

    /** サインインID. */
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** 認可コード. */
    @Id
    @Column(name = "oauth_code")
    public final String oauthCode;

    /** 有効期限. */
    @Column(name = "expires_at")
    public final AppTimeStamp expiresAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param oauthCode 認可コード
     * @param expiresAt 有効期限
     */
    DcUserOauthCodeEntity(
        SignInId signInId,
        String oauthCode,
        AppTimeStamp expiresAt
    ) {
        this.signInId = signInId;
        this.oauthCode = oauthCode;
        this.expiresAt = expiresAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserOauthCodeEntity オブジェクト
     */
    protected DcUserOauthCodeEntity(DcUserOauthCodeEntity org) {
        this.signInId = org.signInId;
        this.oauthCode = org.oauthCode;
        this.expiresAt = org.expiresAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserOauthCodeEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("oauthCode=").append(this.oauthCode).append(", ")
                .append("expiresAt=").append(this.expiresAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.oauthCode, 
                this.expiresAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserOauthCodeEntity other = (DcUserOauthCodeEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.oauthCode, other.oauthCode)
                && Objects.equals(this.expiresAt, other.expiresAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserOauthCodeEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** 認可コード. */
        private String oauthCode;

        /** 有効期限. */
        private AppTimeStamp expiresAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserOauthCodeEntity object
         */
        public DcUserOauthCodeEntity build() {
            return new DcUserOauthCodeEntity(
                    this.signInId, 
                    this.oauthCode, 
                    this.expiresAt
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set oauthCode.
         *
         * @param oauthCode 認可コード
         * @return this builder
         */
        public Builder oauthCode(String oauthCode) {
            this.oauthCode = oauthCode;
            return this;
        }

        /**
         * Set expiresAt.
         *
         * @param expiresAt 有効期限
         * @return this builder
         */
        public Builder expiresAt(AppTimeStamp expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }
    }
}
// @formatter:on
