/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.CustomSignInId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationLogId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationResult;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestBody;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestQuery;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RoleId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * サービスユーザ操作ログ : サービスユーザの画面操作ログを記録.
 */
@Entity(immutable = true)
@Table(name = "service_user_operation_log")
public class ServiceUserOperationLogEntity {

    /** 操作ログID. */
    @Id
    @Column(name = "operation_log_id")
    public final OperationLogId operationLogId;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** サインインID. */
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** カスタムサインインID : ユーザがサインインするためのIDを管理する。. */
    @Column(name = "custom_sign_in_id")
    public final CustomSignInId customSignInId;

    /** ユーザ名. */
    @Column(name = "user_name")
    public final String userName;

    /** ロールID : 画面操作時のロールID. */
    @Column(name = "role_id")
    public final RoleId roleId;

    /** 操作画面ID : 操作画面ID. */
    @Column(name = "screen_id")
    public final String screenId;

    /** トーレスID : リクエストヘッダのX-Amzn-Trace-Id. */
    @Column(name = "trace_id")
    public final String traceId;

    /** 操作結果 : success : 正常
failed : エラー. */
    @Column(name = "operation_result")
    public final OperationResult operationResult;

    /** リクエストURL : URL. */
    @Column(name = "request_url")
    public final String requestUrl;

    /** リクエストメソッド : メソッド. */
    @Column(name = "request_method")
    public final String requestMethod;

    /** リクエストクエリ : GET：APIクエリパラメータ. */
    @Column(name = "request_query")
    public final RequestQuery requestQuery;

    /** リクエストボディ : GET以外：APIリクエストボディ. */
    @Column(name = "request_body")
    public final RequestBody requestBody;

    /** 操作日時 : APIリクエスト時刻. */
    @Column(name = "operated_at")
    public final AppTimeStamp operatedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param operationLogId 操作ログID
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param signInId サインインID
     * @param customSignInId カスタムサインインID : ユーザがサインインするためのIDを管理する。
     * @param userName ユーザ名
     * @param roleId ロールID : 画面操作時のロールID
     * @param screenId 操作画面ID : 操作画面ID
     * @param traceId トーレスID : リクエストヘッダのX-Amzn-Trace-Id
     * @param operationResult 操作結果 : success : 正常
failed : エラー
     * @param requestUrl リクエストURL : URL
     * @param requestMethod リクエストメソッド : メソッド
     * @param requestQuery リクエストクエリ : GET：APIクエリパラメータ
     * @param requestBody リクエストボディ : GET以外：APIリクエストボディ
     * @param operatedAt 操作日時 : APIリクエスト時刻
     */
    ServiceUserOperationLogEntity(
        OperationLogId operationLogId,
        ServiceId serviceId,
        SignInId signInId,
        CustomSignInId customSignInId,
        String userName,
        RoleId roleId,
        String screenId,
        String traceId,
        OperationResult operationResult,
        String requestUrl,
        String requestMethod,
        RequestQuery requestQuery,
        RequestBody requestBody,
        AppTimeStamp operatedAt
    ) {
        this.operationLogId = operationLogId;
        this.serviceId = serviceId;
        this.signInId = signInId;
        this.customSignInId = customSignInId;
        this.userName = userName;
        this.roleId = roleId;
        this.screenId = screenId;
        this.traceId = traceId;
        this.operationResult = operationResult;
        this.requestUrl = requestUrl;
        this.requestMethod = requestMethod;
        this.requestQuery = requestQuery;
        this.requestBody = requestBody;
        this.operatedAt = operatedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org ServiceUserOperationLogEntity オブジェクト
     */
    protected ServiceUserOperationLogEntity(ServiceUserOperationLogEntity org) {
        this.operationLogId = org.operationLogId;
        this.serviceId = org.serviceId;
        this.signInId = org.signInId;
        this.customSignInId = org.customSignInId;
        this.userName = org.userName;
        this.roleId = org.roleId;
        this.screenId = org.screenId;
        this.traceId = org.traceId;
        this.operationResult = org.operationResult;
        this.requestUrl = org.requestUrl;
        this.requestMethod = org.requestMethod;
        this.requestQuery = org.requestQuery;
        this.requestBody = org.requestBody;
        this.operatedAt = org.operatedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("ServiceUserOperationLogEntity [")
                .append("operationLogId=").append(this.operationLogId).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("signInId=").append(this.signInId).append(", ")
                .append("customSignInId=").append(this.customSignInId).append(", ")
                .append("userName=").append(this.userName).append(", ")
                .append("roleId=").append(this.roleId).append(", ")
                .append("screenId=").append(this.screenId).append(", ")
                .append("traceId=").append(this.traceId).append(", ")
                .append("operationResult=").append(this.operationResult).append(", ")
                .append("requestUrl=").append(this.requestUrl).append(", ")
                .append("requestMethod=").append(this.requestMethod).append(", ")
                .append("requestQuery=").append(this.requestQuery).append(", ")
                .append("requestBody=").append(this.requestBody).append(", ")
                .append("operatedAt=").append(this.operatedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.operationLogId, 
                this.serviceId, 
                this.signInId, 
                this.customSignInId, 
                this.userName, 
                this.roleId, 
                this.screenId, 
                this.traceId, 
                this.operationResult, 
                this.requestUrl, 
                this.requestMethod, 
                this.requestQuery, 
                this.requestBody, 
                this.operatedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        ServiceUserOperationLogEntity other = (ServiceUserOperationLogEntity) obj;
        return true
                && Objects.equals(this.operationLogId, other.operationLogId)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.customSignInId, other.customSignInId)
                && Objects.equals(this.userName, other.userName)
                && Objects.equals(this.roleId, other.roleId)
                && Objects.equals(this.screenId, other.screenId)
                && Objects.equals(this.traceId, other.traceId)
                && Objects.equals(this.operationResult, other.operationResult)
                && Objects.equals(this.requestUrl, other.requestUrl)
                && Objects.equals(this.requestMethod, other.requestMethod)
                && Objects.equals(this.requestQuery, other.requestQuery)
                && Objects.equals(this.requestBody, other.requestBody)
                && Objects.equals(this.operatedAt, other.operatedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * ServiceUserOperationLogEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 操作ログID. */
        private OperationLogId operationLogId;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** サインインID. */
        private SignInId signInId;

        /** カスタムサインインID : ユーザがサインインするためのIDを管理する。. */
        private CustomSignInId customSignInId;

        /** ユーザ名. */
        private String userName;

        /** ロールID : 画面操作時のロールID. */
        private RoleId roleId;

        /** 操作画面ID : 操作画面ID. */
        private String screenId;

        /** トーレスID : リクエストヘッダのX-Amzn-Trace-Id. */
        private String traceId;

        /** 操作結果 : success : 正常
failed : エラー. */
        private OperationResult operationResult;

        /** リクエストURL : URL. */
        private String requestUrl;

        /** リクエストメソッド : メソッド. */
        private String requestMethod;

        /** リクエストクエリ : GET：APIクエリパラメータ. */
        private RequestQuery requestQuery;

        /** リクエストボディ : GET以外：APIリクエストボディ. */
        private RequestBody requestBody;

        /** 操作日時 : APIリクエスト時刻. */
        private AppTimeStamp operatedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the ServiceUserOperationLogEntity object
         */
        public ServiceUserOperationLogEntity build() {
            return new ServiceUserOperationLogEntity(
                    this.operationLogId, 
                    this.serviceId, 
                    this.signInId, 
                    this.customSignInId, 
                    this.userName, 
                    this.roleId, 
                    this.screenId, 
                    this.traceId, 
                    this.operationResult, 
                    this.requestUrl, 
                    this.requestMethod, 
                    this.requestQuery, 
                    this.requestBody, 
                    this.operatedAt
            );
        }

        /**
         * Set operationLogId.
         *
         * @param operationLogId 操作ログID
         * @return this builder
         */
        public Builder operationLogId(OperationLogId operationLogId) {
            this.operationLogId = operationLogId;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set customSignInId.
         *
         * @param customSignInId カスタムサインインID : ユーザがサインインするためのIDを管理する。
         * @return this builder
         */
        public Builder customSignInId(CustomSignInId customSignInId) {
            this.customSignInId = customSignInId;
            return this;
        }

        /**
         * Set userName.
         *
         * @param userName ユーザ名
         * @return this builder
         */
        public Builder userName(String userName) {
            this.userName = userName;
            return this;
        }

        /**
         * Set roleId.
         *
         * @param roleId ロールID : 画面操作時のロールID
         * @return this builder
         */
        public Builder roleId(RoleId roleId) {
            this.roleId = roleId;
            return this;
        }

        /**
         * Set screenId.
         *
         * @param screenId 操作画面ID : 操作画面ID
         * @return this builder
         */
        public Builder screenId(String screenId) {
            this.screenId = screenId;
            return this;
        }

        /**
         * Set traceId.
         *
         * @param traceId トーレスID : リクエストヘッダのX-Amzn-Trace-Id
         * @return this builder
         */
        public Builder traceId(String traceId) {
            this.traceId = traceId;
            return this;
        }

        /**
         * Set operationResult.
         *
         * @param operationResult 操作結果 : success : 正常
failed : エラー
         * @return this builder
         */
        public Builder operationResult(OperationResult operationResult) {
            this.operationResult = operationResult;
            return this;
        }

        /**
         * Set requestUrl.
         *
         * @param requestUrl リクエストURL : URL
         * @return this builder
         */
        public Builder requestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
            return this;
        }

        /**
         * Set requestMethod.
         *
         * @param requestMethod リクエストメソッド : メソッド
         * @return this builder
         */
        public Builder requestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
            return this;
        }

        /**
         * Set requestQuery.
         *
         * @param requestQuery リクエストクエリ : GET：APIクエリパラメータ
         * @return this builder
         */
        public Builder requestQuery(RequestQuery requestQuery) {
            this.requestQuery = requestQuery;
            return this;
        }

        /**
         * Set requestBody.
         *
         * @param requestBody リクエストボディ : GET以外：APIリクエストボディ
         * @return this builder
         */
        public Builder requestBody(RequestBody requestBody) {
            this.requestBody = requestBody;
            return this;
        }

        /**
         * Set operatedAt.
         *
         * @param operatedAt 操作日時 : APIリクエスト時刻
         * @return this builder
         */
        public Builder operatedAt(AppTimeStamp operatedAt) {
            this.operatedAt = operatedAt;
            return this;
        }
    }
}
// @formatter:on
