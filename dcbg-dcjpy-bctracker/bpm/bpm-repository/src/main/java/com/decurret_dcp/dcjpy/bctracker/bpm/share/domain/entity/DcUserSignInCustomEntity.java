/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.CustomSignInId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.SignInId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザサインインカスタム : 個人 / 法人ユーザのサインインするためのIDを扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_user_sign_in_custom")
public class DcUserSignInCustomEntity {

    /** サインインID. */
    @Id
    @Column(name = "sign_in_id")
    public final SignInId signInId;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** カスタムサインインID : ユーザがサインインするためのIDを管理する。. */
    @Column(name = "custom_sign_in_id")
    public final CustomSignInId customSignInId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param signInId サインインID
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param customSignInId カスタムサインインID : ユーザがサインインするためのIDを管理する。
     */
    DcUserSignInCustomEntity(
        SignInId signInId,
        ServiceId serviceId,
        CustomSignInId customSignInId
    ) {
        this.signInId = signInId;
        this.serviceId = serviceId;
        this.customSignInId = customSignInId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserSignInCustomEntity オブジェクト
     */
    protected DcUserSignInCustomEntity(DcUserSignInCustomEntity org) {
        this.signInId = org.signInId;
        this.serviceId = org.serviceId;
        this.customSignInId = org.customSignInId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserSignInCustomEntity [")
                .append("signInId=").append(this.signInId).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("customSignInId=").append(this.customSignInId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.signInId, 
                this.serviceId, 
                this.customSignInId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserSignInCustomEntity other = (DcUserSignInCustomEntity) obj;
        return true
                && Objects.equals(this.signInId, other.signInId)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.customSignInId, other.customSignInId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserSignInCustomEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** サインインID. */
        private SignInId signInId;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** カスタムサインインID : ユーザがサインインするためのIDを管理する。. */
        private CustomSignInId customSignInId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserSignInCustomEntity object
         */
        public DcUserSignInCustomEntity build() {
            return new DcUserSignInCustomEntity(
                    this.signInId, 
                    this.serviceId, 
                    this.customSignInId
            );
        }

        /**
         * Set signInId.
         *
         * @param signInId サインインID
         * @return this builder
         */
        public Builder signInId(SignInId signInId) {
            this.signInId = signInId;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set customSignInId.
         *
         * @param customSignInId カスタムサインインID : ユーザがサインインするためのIDを管理する。
         * @return this builder
         */
        public Builder customSignInId(CustomSignInId customSignInId) {
            this.customSignInId = customSignInId;
            return this;
        }
    }
}
// @formatter:on
