/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.IbOperationType;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * IB認証手続きOAuth状態 : IB認証手続きの OAuth で連携する際の state を管理する。DCユーザのstate管理はDCユーザOAuth状態で行う。.
 */
@Entity(immutable = true)
@Table(name = "ib_oauth_state")
public class IbOauthStateEntity {

    /** IB認証ステートID : IB認証手続き前後のセッションが同一であることを確認するOAuth認証のステートを表す。. */
    @Id
    @Column(name = "ib_oauth_state_id")
    public final String ibOauthStateId;

    /** 有効期限. */
    @Column(name = "expires_at")
    public final AppTimeStamp expiresAt;

    /** IB認証手続き種類 : IB認証手続きの内容を表す。
sign_in_id_inquiry：サインインID照会
temporary_password_issuarance：仮パスワード発行. */
    @Column(name = "ib_operation_type")
    public final IbOperationType ibOperationType;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param ibOauthStateId IB認証ステートID : IB認証手続き前後のセッションが同一であることを確認するOAuth認証のステートを表す。
     * @param expiresAt 有効期限
     * @param ibOperationType IB認証手続き種類 : IB認証手続きの内容を表す。
sign_in_id_inquiry：サインインID照会
temporary_password_issuarance：仮パスワード発行
     */
    IbOauthStateEntity(
        String ibOauthStateId,
        AppTimeStamp expiresAt,
        IbOperationType ibOperationType
    ) {
        this.ibOauthStateId = ibOauthStateId;
        this.expiresAt = expiresAt;
        this.ibOperationType = ibOperationType;
    }

    /**
     * コンストラクタ。
     * 
     * @param org IbOauthStateEntity オブジェクト
     */
    protected IbOauthStateEntity(IbOauthStateEntity org) {
        this.ibOauthStateId = org.ibOauthStateId;
        this.expiresAt = org.expiresAt;
        this.ibOperationType = org.ibOperationType;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("IbOauthStateEntity [")
                .append("ibOauthStateId=").append(this.ibOauthStateId).append(", ")
                .append("expiresAt=").append(this.expiresAt).append(", ")
                .append("ibOperationType=").append(this.ibOperationType)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.ibOauthStateId, 
                this.expiresAt, 
                this.ibOperationType
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        IbOauthStateEntity other = (IbOauthStateEntity) obj;
        return true
                && Objects.equals(this.ibOauthStateId, other.ibOauthStateId)
                && Objects.equals(this.expiresAt, other.expiresAt)
                && Objects.equals(this.ibOperationType, other.ibOperationType)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * IbOauthStateEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** IB認証ステートID : IB認証手続き前後のセッションが同一であることを確認するOAuth認証のステートを表す。. */
        private String ibOauthStateId;

        /** 有効期限. */
        private AppTimeStamp expiresAt;

        /** IB認証手続き種類 : IB認証手続きの内容を表す。
sign_in_id_inquiry：サインインID照会
temporary_password_issuarance：仮パスワード発行. */
        private IbOperationType ibOperationType;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the IbOauthStateEntity object
         */
        public IbOauthStateEntity build() {
            return new IbOauthStateEntity(
                    this.ibOauthStateId, 
                    this.expiresAt, 
                    this.ibOperationType
            );
        }

        /**
         * Set ibOauthStateId.
         *
         * @param ibOauthStateId IB認証ステートID : IB認証手続き前後のセッションが同一であることを確認するOAuth認証のステートを表す。
         * @return this builder
         */
        public Builder ibOauthStateId(String ibOauthStateId) {
            this.ibOauthStateId = ibOauthStateId;
            return this;
        }

        /**
         * Set expiresAt.
         *
         * @param expiresAt 有効期限
         * @return this builder
         */
        public Builder expiresAt(AppTimeStamp expiresAt) {
            this.expiresAt = expiresAt;
            return this;
        }

        /**
         * Set ibOperationType.
         *
         * @param ibOperationType IB認証手続き種類 : IB認証手続きの内容を表す。
sign_in_id_inquiry：サインインID照会
temporary_password_issuarance：仮パスワード発行
         * @return this builder
         */
        public Builder ibOperationType(IbOperationType ibOperationType) {
            this.ibOperationType = ibOperationType;
            return this;
        }
    }
}
// @formatter:on
