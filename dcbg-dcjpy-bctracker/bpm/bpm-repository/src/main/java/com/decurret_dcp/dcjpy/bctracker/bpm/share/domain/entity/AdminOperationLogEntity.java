/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.OperationLogId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestBody;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.RequestQuery;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * Admin操作ログ : adminユーザ、batch処理での操作ログを記録.
 */
@Entity(immutable = true)
@Table(name = "admin_operation_log")
public class AdminOperationLogEntity {

    /** 操作ログID. */
    @Id
    @Column(name = "operation_log_id")
    public final OperationLogId operationLogId;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** トーレスID : リクエストヘッダのX-Amzn-Trace-Id. */
    @Column(name = "trace_id")
    public final String traceId;

    /** 操作結果 : success : 正常
failed : エラー. */
    @Column(name = "operaton_result")
    public final String operatonResult;

    /** リクエストURL : URL. */
    @Column(name = "request_url")
    public final String requestUrl;

    /** リクエストメソッド : メソッド. */
    @Column(name = "request_method")
    public final String requestMethod;

    /** リクエストクエリ : GET：APIクエリパラメータ. */
    @Column(name = "request_query")
    public final RequestQuery requestQuery;

    /** リクエストボディ : GET以外：APIリクエストボディ. */
    @Column(name = "request_body")
    public final RequestBody requestBody;

    /** 操作日時 : APIリクエスト時刻. */
    @Column(name = "operated_at")
    public final AppTimeStamp operatedAt;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param operationLogId 操作ログID
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param traceId トーレスID : リクエストヘッダのX-Amzn-Trace-Id
     * @param operatonResult 操作結果 : success : 正常
failed : エラー
     * @param requestUrl リクエストURL : URL
     * @param requestMethod リクエストメソッド : メソッド
     * @param requestQuery リクエストクエリ : GET：APIクエリパラメータ
     * @param requestBody リクエストボディ : GET以外：APIリクエストボディ
     * @param operatedAt 操作日時 : APIリクエスト時刻
     */
    AdminOperationLogEntity(
        OperationLogId operationLogId,
        ServiceId serviceId,
        String traceId,
        String operatonResult,
        String requestUrl,
        String requestMethod,
        RequestQuery requestQuery,
        RequestBody requestBody,
        AppTimeStamp operatedAt
    ) {
        this.operationLogId = operationLogId;
        this.serviceId = serviceId;
        this.traceId = traceId;
        this.operatonResult = operatonResult;
        this.requestUrl = requestUrl;
        this.requestMethod = requestMethod;
        this.requestQuery = requestQuery;
        this.requestBody = requestBody;
        this.operatedAt = operatedAt;
    }

    /**
     * コンストラクタ。
     * 
     * @param org AdminOperationLogEntity オブジェクト
     */
    protected AdminOperationLogEntity(AdminOperationLogEntity org) {
        this.operationLogId = org.operationLogId;
        this.serviceId = org.serviceId;
        this.traceId = org.traceId;
        this.operatonResult = org.operatonResult;
        this.requestUrl = org.requestUrl;
        this.requestMethod = org.requestMethod;
        this.requestQuery = org.requestQuery;
        this.requestBody = org.requestBody;
        this.operatedAt = org.operatedAt;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("AdminOperationLogEntity [")
                .append("operationLogId=").append(this.operationLogId).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("traceId=").append(this.traceId).append(", ")
                .append("operatonResult=").append(this.operatonResult).append(", ")
                .append("requestUrl=").append(this.requestUrl).append(", ")
                .append("requestMethod=").append(this.requestMethod).append(", ")
                .append("requestQuery=").append(this.requestQuery).append(", ")
                .append("requestBody=").append(this.requestBody).append(", ")
                .append("operatedAt=").append(this.operatedAt)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.operationLogId, 
                this.serviceId, 
                this.traceId, 
                this.operatonResult, 
                this.requestUrl, 
                this.requestMethod, 
                this.requestQuery, 
                this.requestBody, 
                this.operatedAt
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        AdminOperationLogEntity other = (AdminOperationLogEntity) obj;
        return true
                && Objects.equals(this.operationLogId, other.operationLogId)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.traceId, other.traceId)
                && Objects.equals(this.operatonResult, other.operatonResult)
                && Objects.equals(this.requestUrl, other.requestUrl)
                && Objects.equals(this.requestMethod, other.requestMethod)
                && Objects.equals(this.requestQuery, other.requestQuery)
                && Objects.equals(this.requestBody, other.requestBody)
                && Objects.equals(this.operatedAt, other.operatedAt)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * AdminOperationLogEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 操作ログID. */
        private OperationLogId operationLogId;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** トーレスID : リクエストヘッダのX-Amzn-Trace-Id. */
        private String traceId;

        /** 操作結果 : success : 正常
failed : エラー. */
        private String operatonResult;

        /** リクエストURL : URL. */
        private String requestUrl;

        /** リクエストメソッド : メソッド. */
        private String requestMethod;

        /** リクエストクエリ : GET：APIクエリパラメータ. */
        private RequestQuery requestQuery;

        /** リクエストボディ : GET以外：APIリクエストボディ. */
        private RequestBody requestBody;

        /** 操作日時 : APIリクエスト時刻. */
        private AppTimeStamp operatedAt;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the AdminOperationLogEntity object
         */
        public AdminOperationLogEntity build() {
            return new AdminOperationLogEntity(
                    this.operationLogId, 
                    this.serviceId, 
                    this.traceId, 
                    this.operatonResult, 
                    this.requestUrl, 
                    this.requestMethod, 
                    this.requestQuery, 
                    this.requestBody, 
                    this.operatedAt
            );
        }

        /**
         * Set operationLogId.
         *
         * @param operationLogId 操作ログID
         * @return this builder
         */
        public Builder operationLogId(OperationLogId operationLogId) {
            this.operationLogId = operationLogId;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set traceId.
         *
         * @param traceId トーレスID : リクエストヘッダのX-Amzn-Trace-Id
         * @return this builder
         */
        public Builder traceId(String traceId) {
            this.traceId = traceId;
            return this;
        }

        /**
         * Set operatonResult.
         *
         * @param operatonResult 操作結果 : success : 正常
failed : エラー
         * @return this builder
         */
        public Builder operatonResult(String operatonResult) {
            this.operatonResult = operatonResult;
            return this;
        }

        /**
         * Set requestUrl.
         *
         * @param requestUrl リクエストURL : URL
         * @return this builder
         */
        public Builder requestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
            return this;
        }

        /**
         * Set requestMethod.
         *
         * @param requestMethod リクエストメソッド : メソッド
         * @return this builder
         */
        public Builder requestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
            return this;
        }

        /**
         * Set requestQuery.
         *
         * @param requestQuery リクエストクエリ : GET：APIクエリパラメータ
         * @return this builder
         */
        public Builder requestQuery(RequestQuery requestQuery) {
            this.requestQuery = requestQuery;
            return this;
        }

        /**
         * Set requestBody.
         *
         * @param requestBody リクエストボディ : GET以外：APIリクエストボディ
         * @return this builder
         */
        public Builder requestBody(RequestBody requestBody) {
            this.requestBody = requestBody;
            return this;
        }

        /**
         * Set operatedAt.
         *
         * @param operatedAt 操作日時 : APIリクエスト時刻
         * @return this builder
         */
        public Builder operatedAt(AppTimeStamp operatedAt) {
            this.operatedAt = operatedAt;
            return this;
        }
    }
}
// @formatter:on
