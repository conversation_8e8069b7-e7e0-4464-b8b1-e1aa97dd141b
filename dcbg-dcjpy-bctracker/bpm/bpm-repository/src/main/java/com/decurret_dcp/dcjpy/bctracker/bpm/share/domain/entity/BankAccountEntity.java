/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * 銀行預金口座 : DCアカウントに紐づく銀行預金口座情報を扱う。.
 */
@Entity(immutable = true)
@Table(name = "bank_account")
public class BankAccountEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** 銀行預金口座ID. */
    @Column(name = "bank_account_id")
    public final String bankAccountId;

    /** 銀行コード. */
    @Column(name = "bank_code")
    public final String bankCode;

    /** 銀行名. */
    @Column(name = "bank_name")
    public final String bankName;

    /** 支店コード. */
    @Column(name = "branch_code")
    public final String branchCode;

    /** 銀行預金口座種別 : saving : 普通口座
checking : 貯蓄口座. */
    @Column(name = "bank_account_type")
    public final String bankAccountType;

    /** 銀行預金口座番号. */
    @Column(name = "bank_account_number")
    public final String bankAccountNumber;

    /** 銀行預金口座名義. */
    @Column(name = "bank_account_name")
    public final String bankAccountName;

    /** 記号 : ゆうちょの場合、記号を設定. */
    @Column(name = "jpbank_account_code")
    public final String jpbankAccountCode;

    /** 番号 : ゆうちょの場合、番号を設定（前ゼロ埋め）. */
    @Column(name = "jpbank_account_number")
    public final String jpbankAccountNumber;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param bankAccountId 銀行預金口座ID
     * @param bankCode 銀行コード
     * @param bankName 銀行名
     * @param branchCode 支店コード
     * @param bankAccountType 銀行預金口座種別 : saving : 普通口座
checking : 貯蓄口座
     * @param bankAccountNumber 銀行預金口座番号
     * @param bankAccountName 銀行預金口座名義
     * @param jpbankAccountCode 記号 : ゆうちょの場合、記号を設定
     * @param jpbankAccountNumber 番号 : ゆうちょの場合、番号を設定（前ゼロ埋め）
     */
    BankAccountEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        String bankAccountId,
        String bankCode,
        String bankName,
        String branchCode,
        String bankAccountType,
        String bankAccountNumber,
        String bankAccountName,
        String jpbankAccountCode,
        String jpbankAccountNumber
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.bankAccountId = bankAccountId;
        this.bankCode = bankCode;
        this.bankName = bankName;
        this.branchCode = branchCode;
        this.bankAccountType = bankAccountType;
        this.bankAccountNumber = bankAccountNumber;
        this.bankAccountName = bankAccountName;
        this.jpbankAccountCode = jpbankAccountCode;
        this.jpbankAccountNumber = jpbankAccountNumber;
    }

    /**
     * コンストラクタ。
     * 
     * @param org BankAccountEntity オブジェクト
     */
    protected BankAccountEntity(BankAccountEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.bankAccountId = org.bankAccountId;
        this.bankCode = org.bankCode;
        this.bankName = org.bankName;
        this.branchCode = org.branchCode;
        this.bankAccountType = org.bankAccountType;
        this.bankAccountNumber = org.bankAccountNumber;
        this.bankAccountName = org.bankAccountName;
        this.jpbankAccountCode = org.jpbankAccountCode;
        this.jpbankAccountNumber = org.jpbankAccountNumber;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("BankAccountEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("bankAccountId=").append(this.bankAccountId).append(", ")
                .append("bankCode=").append(this.bankCode).append(", ")
                .append("bankName=").append(this.bankName).append(", ")
                .append("branchCode=").append(this.branchCode).append(", ")
                .append("bankAccountType=").append(this.bankAccountType).append(", ")
                .append("bankAccountNumber=").append(this.bankAccountNumber).append(", ")
                .append("bankAccountName=").append(this.bankAccountName).append(", ")
                .append("jpbankAccountCode=").append(this.jpbankAccountCode).append(", ")
                .append("jpbankAccountNumber=").append(this.jpbankAccountNumber)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.bankAccountId, 
                this.bankCode, 
                this.bankName, 
                this.branchCode, 
                this.bankAccountType, 
                this.bankAccountNumber, 
                this.bankAccountName, 
                this.jpbankAccountCode, 
                this.jpbankAccountNumber
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        BankAccountEntity other = (BankAccountEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.bankAccountId, other.bankAccountId)
                && Objects.equals(this.bankCode, other.bankCode)
                && Objects.equals(this.bankName, other.bankName)
                && Objects.equals(this.branchCode, other.branchCode)
                && Objects.equals(this.bankAccountType, other.bankAccountType)
                && Objects.equals(this.bankAccountNumber, other.bankAccountNumber)
                && Objects.equals(this.bankAccountName, other.bankAccountName)
                && Objects.equals(this.jpbankAccountCode, other.jpbankAccountCode)
                && Objects.equals(this.jpbankAccountNumber, other.jpbankAccountNumber)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * BankAccountEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** 銀行預金口座ID. */
        private String bankAccountId;

        /** 銀行コード. */
        private String bankCode;

        /** 銀行名. */
        private String bankName;

        /** 支店コード. */
        private String branchCode;

        /** 銀行預金口座種別 : saving : 普通口座
checking : 貯蓄口座. */
        private String bankAccountType;

        /** 銀行預金口座番号. */
        private String bankAccountNumber;

        /** 銀行預金口座名義. */
        private String bankAccountName;

        /** 記号 : ゆうちょの場合、記号を設定. */
        private String jpbankAccountCode;

        /** 番号 : ゆうちょの場合、番号を設定（前ゼロ埋め）. */
        private String jpbankAccountNumber;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the BankAccountEntity object
         */
        public BankAccountEntity build() {
            return new BankAccountEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.bankAccountId, 
                    this.bankCode, 
                    this.bankName, 
                    this.branchCode, 
                    this.bankAccountType, 
                    this.bankAccountNumber, 
                    this.bankAccountName, 
                    this.jpbankAccountCode, 
                    this.jpbankAccountNumber
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set bankAccountId.
         *
         * @param bankAccountId 銀行預金口座ID
         * @return this builder
         */
        public Builder bankAccountId(String bankAccountId) {
            this.bankAccountId = bankAccountId;
            return this;
        }

        /**
         * Set bankCode.
         *
         * @param bankCode 銀行コード
         * @return this builder
         */
        public Builder bankCode(String bankCode) {
            this.bankCode = bankCode;
            return this;
        }

        /**
         * Set bankName.
         *
         * @param bankName 銀行名
         * @return this builder
         */
        public Builder bankName(String bankName) {
            this.bankName = bankName;
            return this;
        }

        /**
         * Set branchCode.
         *
         * @param branchCode 支店コード
         * @return this builder
         */
        public Builder branchCode(String branchCode) {
            this.branchCode = branchCode;
            return this;
        }

        /**
         * Set bankAccountType.
         *
         * @param bankAccountType 銀行預金口座種別 : saving : 普通口座
checking : 貯蓄口座
         * @return this builder
         */
        public Builder bankAccountType(String bankAccountType) {
            this.bankAccountType = bankAccountType;
            return this;
        }

        /**
         * Set bankAccountNumber.
         *
         * @param bankAccountNumber 銀行預金口座番号
         * @return this builder
         */
        public Builder bankAccountNumber(String bankAccountNumber) {
            this.bankAccountNumber = bankAccountNumber;
            return this;
        }

        /**
         * Set bankAccountName.
         *
         * @param bankAccountName 銀行預金口座名義
         * @return this builder
         */
        public Builder bankAccountName(String bankAccountName) {
            this.bankAccountName = bankAccountName;
            return this;
        }

        /**
         * Set jpbankAccountCode.
         *
         * @param jpbankAccountCode 記号 : ゆうちょの場合、記号を設定
         * @return this builder
         */
        public Builder jpbankAccountCode(String jpbankAccountCode) {
            this.jpbankAccountCode = jpbankAccountCode;
            return this;
        }

        /**
         * Set jpbankAccountNumber.
         *
         * @param jpbankAccountNumber 番号 : ゆうちょの場合、番号を設定（前ゼロ埋め）
         * @return this builder
         */
        public Builder jpbankAccountNumber(String jpbankAccountNumber) {
            this.jpbankAccountNumber = jpbankAccountNumber;
            return this;
        }
    }
}
// @formatter:on
