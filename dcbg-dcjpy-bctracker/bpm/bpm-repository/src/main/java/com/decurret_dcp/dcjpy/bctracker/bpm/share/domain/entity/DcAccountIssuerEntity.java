/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.IssuerId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DC口座イシュア.
 */
@Entity(immutable = true)
@Table(name = "dc_account_issuer")
public class DcAccountIssuerEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** イシュアID. */
    @Column(name = "issuer_id")
    public final IssuerId issuerId;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param issuerId イシュアID
     */
    DcAccountIssuerEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        IssuerId issuerId
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.issuerId = issuerId;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcAccountIssuerEntity オブジェクト
     */
    protected DcAccountIssuerEntity(DcAccountIssuerEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.issuerId = org.issuerId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcAccountIssuerEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("issuerId=").append(this.issuerId)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.issuerId
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcAccountIssuerEntity other = (DcAccountIssuerEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.issuerId, other.issuerId)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcAccountIssuerEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** イシュアID. */
        private IssuerId issuerId;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcAccountIssuerEntity object
         */
        public DcAccountIssuerEntity build() {
            return new DcAccountIssuerEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.issuerId
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set issuerId.
         *
         * @param issuerId イシュアID
         * @return this builder
         */
        public Builder issuerId(IssuerId issuerId) {
            this.issuerId = issuerId;
            return this;
        }
    }
}
// @formatter:on
