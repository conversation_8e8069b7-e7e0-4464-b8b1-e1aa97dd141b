/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DCユーザ操作マスタテーブル : DCユーザが操作する画面、操作内容、絞り込み条件を管理するマスタ.
 */
@Entity(immutable = true)
@Table(name = "dc_user_operation_master")
public class DcUserOperationMasterEntity {

    /** 操作画面ID. */
    @Id
    @Column(name = "screen_id")
    public final String screenId;

    /** 操作内容. */
    @Id
    @Column(name = "operation_name")
    public final String operationName;

    /** リクエストURLパターン. */
    @Column(name = "request_url_pattern")
    public final String requestUrlPattern;

    /** リクエストメソッド : メソッド. */
    @Column(name = "request_method")
    public final String requestMethod;

    /** 画面名称. */
    @Column(name = "screen_name")
    public final String screenName;

    /** 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}. */
    @Column(name = "operation_conditions")
    public final String operationConditions;

    /** 操作画面ユーザ種別 : individual(個人) / company(法人). */
    @Column(name = "screen_dc_user_type")
    public final String screenDcUserType;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param screenId 操作画面ID
     * @param operationName 操作内容
     * @param requestUrlPattern リクエストURLパターン
     * @param requestMethod リクエストメソッド : メソッド
     * @param screenName 画面名称
     * @param operationConditions 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}
     * @param screenDcUserType 操作画面ユーザ種別 : individual(個人) / company(法人)
     */
    DcUserOperationMasterEntity(
        String screenId,
        String operationName,
        String requestUrlPattern,
        String requestMethod,
        String screenName,
        String operationConditions,
        String screenDcUserType
    ) {
        this.screenId = screenId;
        this.operationName = operationName;
        this.requestUrlPattern = requestUrlPattern;
        this.requestMethod = requestMethod;
        this.screenName = screenName;
        this.operationConditions = operationConditions;
        this.screenDcUserType = screenDcUserType;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcUserOperationMasterEntity オブジェクト
     */
    protected DcUserOperationMasterEntity(DcUserOperationMasterEntity org) {
        this.screenId = org.screenId;
        this.operationName = org.operationName;
        this.requestUrlPattern = org.requestUrlPattern;
        this.requestMethod = org.requestMethod;
        this.screenName = org.screenName;
        this.operationConditions = org.operationConditions;
        this.screenDcUserType = org.screenDcUserType;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcUserOperationMasterEntity [")
                .append("screenId=").append(this.screenId).append(", ")
                .append("operationName=").append(this.operationName).append(", ")
                .append("requestUrlPattern=").append(this.requestUrlPattern).append(", ")
                .append("requestMethod=").append(this.requestMethod).append(", ")
                .append("screenName=").append(this.screenName).append(", ")
                .append("operationConditions=").append(this.operationConditions).append(", ")
                .append("screenDcUserType=").append(this.screenDcUserType)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.screenId, 
                this.operationName, 
                this.requestUrlPattern, 
                this.requestMethod, 
                this.screenName, 
                this.operationConditions, 
                this.screenDcUserType
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcUserOperationMasterEntity other = (DcUserOperationMasterEntity) obj;
        return true
                && Objects.equals(this.screenId, other.screenId)
                && Objects.equals(this.operationName, other.operationName)
                && Objects.equals(this.requestUrlPattern, other.requestUrlPattern)
                && Objects.equals(this.requestMethod, other.requestMethod)
                && Objects.equals(this.screenName, other.screenName)
                && Objects.equals(this.operationConditions, other.operationConditions)
                && Objects.equals(this.screenDcUserType, other.screenDcUserType)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcUserOperationMasterEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** 操作画面ID. */
        private String screenId;

        /** 操作内容. */
        private String operationName;

        /** リクエストURLパターン. */
        private String requestUrlPattern;

        /** リクエストメソッド : メソッド. */
        private String requestMethod;

        /** 画面名称. */
        private String screenName;

        /** 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}. */
        private String operationConditions;

        /** 操作画面ユーザ種別 : individual(個人) / company(法人). */
        private String screenDcUserType;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcUserOperationMasterEntity object
         */
        public DcUserOperationMasterEntity build() {
            return new DcUserOperationMasterEntity(
                    this.screenId, 
                    this.operationName, 
                    this.requestUrlPattern, 
                    this.requestMethod, 
                    this.screenName, 
                    this.operationConditions, 
                    this.screenDcUserType
            );
        }

        /**
         * Set screenId.
         *
         * @param screenId 操作画面ID
         * @return this builder
         */
        public Builder screenId(String screenId) {
            this.screenId = screenId;
            return this;
        }

        /**
         * Set operationName.
         *
         * @param operationName 操作内容
         * @return this builder
         */
        public Builder operationName(String operationName) {
            this.operationName = operationName;
            return this;
        }

        /**
         * Set requestUrlPattern.
         *
         * @param requestUrlPattern リクエストURLパターン
         * @return this builder
         */
        public Builder requestUrlPattern(String requestUrlPattern) {
            this.requestUrlPattern = requestUrlPattern;
            return this;
        }

        /**
         * Set requestMethod.
         *
         * @param requestMethod リクエストメソッド : メソッド
         * @return this builder
         */
        public Builder requestMethod(String requestMethod) {
            this.requestMethod = requestMethod;
            return this;
        }

        /**
         * Set screenName.
         *
         * @param screenName 画面名称
         * @return this builder
         */
        public Builder screenName(String screenName) {
            this.screenName = screenName;
            return this;
        }

        /**
         * Set operationConditions.
         *
         * @param operationConditions 操作条件 : 操作ログのリクエストボディから操作内容を特定するための条件。リクエストボディで検索したキーとバリューの形で設定する

(例：
{
    { “review_status“ : “approval” },
    { “review_status“ : “reject” }
}
         * @return this builder
         */
        public Builder operationConditions(String operationConditions) {
            this.operationConditions = operationConditions;
            return this;
        }

        /**
         * Set screenDcUserType.
         *
         * @param screenDcUserType 操作画面ユーザ種別 : individual(個人) / company(法人)
         * @return this builder
         */
        public Builder screenDcUserType(String screenDcUserType) {
            this.screenDcUserType = screenDcUserType;
            return this;
        }
    }
}
// @formatter:on
