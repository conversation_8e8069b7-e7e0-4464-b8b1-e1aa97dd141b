/*
 * Copyright (c) 2023. DeCurret DCP Inc. All Rights Reserved.
 *
 * [WARNING] This file is auto generated by domaCodeGen task in dcbg-dcf-bpm-migration repository.
 */
// @formatter:off
package com.decurret_dcp.dcjpy.bctracker.bpm.share.domain.entity;

import java.util.Objects;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.DcBankNumber;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ServiceId;
import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

/**
 * DC口座設定可能限度 : 各口座アカウントの取引毎の設定可能限度額情報を扱う。.
 */
@Entity(immutable = true)
@Table(name = "dc_account_limit")
public class DcAccountLimitEntity {

    /** DC口座番号. */
    @Id
    @Column(name = "dc_bank_number")
    public final DcBankNumber dcBankNumber;

    /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
    @Id
    @Column(name = "service_id")
    public final ServiceId serviceId;

    /** 1回あたりの発行設定可能限度額. */
    @Column(name = "once_mint_limit_max")
    public final Amount onceMintLimitMax;

    /** 1回あたりの償却設定可能限度額. */
    @Column(name = "once_burn_limit_max")
    public final Amount onceBurnLimitMax;

    /** 1回あたりの移転設定可能限度額. */
    @Column(name = "once_transfer_limit_max")
    public final Amount onceTransferLimitMax;

    /** 1回あたりのチャージ設定可能限度額. */
    @Column(name = "once_charge_limit_max")
    public final Amount onceChargeLimitMax;

    /** 1回あたりのディスチャージ設定可能限度額. */
    @Column(name = "once_discharge_limit_max")
    public final Amount onceDischargeLimitMax;

    /** 1日の発行累積額の設定可能限度額. */
    @Column(name = "daily_mint_limit_max")
    public final Amount dailyMintLimitMax;

    /** 1日の償却累積額の設定可能限度額. */
    @Column(name = "daily_burn_limit_max")
    public final Amount dailyBurnLimitMax;

    /** 1日の移転累積額の設定可能限度額. */
    @Column(name = "daily_transfer_limit_max")
    public final Amount dailyTransferLimitMax;

    /** 1日のチャージ累積額の設定可能限度額. */
    @Column(name = "daily_charge_limit_max")
    public final Amount dailyChargeLimitMax;

    /** 1日のディスチャージ累積額の設定可能限度額. */
    @Column(name = "daily_discharge_limit_max")
    public final Amount dailyDischargeLimitMax;

    /** 1日の累積限度額の設定可能限度額. */
    @Column(name = "daily_cumulative_limit_max")
    public final Amount dailyCumulativeLimitMax;

    /**
     * コンストラクタ。(doma2 が内部で利用する想定)
     * 
     * @param dcBankNumber DC口座番号
     * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
     * @param onceMintLimitMax 1回あたりの発行設定可能限度額
     * @param onceBurnLimitMax 1回あたりの償却設定可能限度額
     * @param onceTransferLimitMax 1回あたりの移転設定可能限度額
     * @param onceChargeLimitMax 1回あたりのチャージ設定可能限度額
     * @param onceDischargeLimitMax 1回あたりのディスチャージ設定可能限度額
     * @param dailyMintLimitMax 1日の発行累積額の設定可能限度額
     * @param dailyBurnLimitMax 1日の償却累積額の設定可能限度額
     * @param dailyTransferLimitMax 1日の移転累積額の設定可能限度額
     * @param dailyChargeLimitMax 1日のチャージ累積額の設定可能限度額
     * @param dailyDischargeLimitMax 1日のディスチャージ累積額の設定可能限度額
     * @param dailyCumulativeLimitMax 1日の累積限度額の設定可能限度額
     */
    DcAccountLimitEntity(
        DcBankNumber dcBankNumber,
        ServiceId serviceId,
        Amount onceMintLimitMax,
        Amount onceBurnLimitMax,
        Amount onceTransferLimitMax,
        Amount onceChargeLimitMax,
        Amount onceDischargeLimitMax,
        Amount dailyMintLimitMax,
        Amount dailyBurnLimitMax,
        Amount dailyTransferLimitMax,
        Amount dailyChargeLimitMax,
        Amount dailyDischargeLimitMax,
        Amount dailyCumulativeLimitMax
    ) {
        this.dcBankNumber = dcBankNumber;
        this.serviceId = serviceId;
        this.onceMintLimitMax = onceMintLimitMax;
        this.onceBurnLimitMax = onceBurnLimitMax;
        this.onceTransferLimitMax = onceTransferLimitMax;
        this.onceChargeLimitMax = onceChargeLimitMax;
        this.onceDischargeLimitMax = onceDischargeLimitMax;
        this.dailyMintLimitMax = dailyMintLimitMax;
        this.dailyBurnLimitMax = dailyBurnLimitMax;
        this.dailyTransferLimitMax = dailyTransferLimitMax;
        this.dailyChargeLimitMax = dailyChargeLimitMax;
        this.dailyDischargeLimitMax = dailyDischargeLimitMax;
        this.dailyCumulativeLimitMax = dailyCumulativeLimitMax;
    }

    /**
     * コンストラクタ。
     * 
     * @param org DcAccountLimitEntity オブジェクト
     */
    protected DcAccountLimitEntity(DcAccountLimitEntity org) {
        this.dcBankNumber = org.dcBankNumber;
        this.serviceId = org.serviceId;
        this.onceMintLimitMax = org.onceMintLimitMax;
        this.onceBurnLimitMax = org.onceBurnLimitMax;
        this.onceTransferLimitMax = org.onceTransferLimitMax;
        this.onceChargeLimitMax = org.onceChargeLimitMax;
        this.onceDischargeLimitMax = org.onceDischargeLimitMax;
        this.dailyMintLimitMax = org.dailyMintLimitMax;
        this.dailyBurnLimitMax = org.dailyBurnLimitMax;
        this.dailyTransferLimitMax = org.dailyTransferLimitMax;
        this.dailyChargeLimitMax = org.dailyChargeLimitMax;
        this.dailyDischargeLimitMax = org.dailyDischargeLimitMax;
        this.dailyCumulativeLimitMax = org.dailyCumulativeLimitMax;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString() {
        return new StringBuilder().append("DcAccountLimitEntity [")
                .append("dcBankNumber=").append(this.dcBankNumber).append(", ")
                .append("serviceId=").append(this.serviceId).append(", ")
                .append("onceMintLimitMax=").append(this.onceMintLimitMax).append(", ")
                .append("onceBurnLimitMax=").append(this.onceBurnLimitMax).append(", ")
                .append("onceTransferLimitMax=").append(this.onceTransferLimitMax).append(", ")
                .append("onceChargeLimitMax=").append(this.onceChargeLimitMax).append(", ")
                .append("onceDischargeLimitMax=").append(this.onceDischargeLimitMax).append(", ")
                .append("dailyMintLimitMax=").append(this.dailyMintLimitMax).append(", ")
                .append("dailyBurnLimitMax=").append(this.dailyBurnLimitMax).append(", ")
                .append("dailyTransferLimitMax=").append(this.dailyTransferLimitMax).append(", ")
                .append("dailyChargeLimitMax=").append(this.dailyChargeLimitMax).append(", ")
                .append("dailyDischargeLimitMax=").append(this.dailyDischargeLimitMax).append(", ")
                .append("dailyCumulativeLimitMax=").append(this.dailyCumulativeLimitMax)
                .append("]").toString();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        return prime * super.hashCode()+ Objects.hash(
                this.dcBankNumber, 
                this.serviceId, 
                this.onceMintLimitMax, 
                this.onceBurnLimitMax, 
                this.onceTransferLimitMax, 
                this.onceChargeLimitMax, 
                this.onceDischargeLimitMax, 
                this.dailyMintLimitMax, 
                this.dailyBurnLimitMax, 
                this.dailyTransferLimitMax, 
                this.dailyChargeLimitMax, 
                this.dailyDischargeLimitMax, 
                this.dailyCumulativeLimitMax
        );
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        DcAccountLimitEntity other = (DcAccountLimitEntity) obj;
        return true
                && Objects.equals(this.dcBankNumber, other.dcBankNumber)
                && Objects.equals(this.serviceId, other.serviceId)
                && Objects.equals(this.onceMintLimitMax, other.onceMintLimitMax)
                && Objects.equals(this.onceBurnLimitMax, other.onceBurnLimitMax)
                && Objects.equals(this.onceTransferLimitMax, other.onceTransferLimitMax)
                && Objects.equals(this.onceChargeLimitMax, other.onceChargeLimitMax)
                && Objects.equals(this.onceDischargeLimitMax, other.onceDischargeLimitMax)
                && Objects.equals(this.dailyMintLimitMax, other.dailyMintLimitMax)
                && Objects.equals(this.dailyBurnLimitMax, other.dailyBurnLimitMax)
                && Objects.equals(this.dailyTransferLimitMax, other.dailyTransferLimitMax)
                && Objects.equals(this.dailyChargeLimitMax, other.dailyChargeLimitMax)
                && Objects.equals(this.dailyDischargeLimitMax, other.dailyDischargeLimitMax)
                && Objects.equals(this.dailyCumulativeLimitMax, other.dailyCumulativeLimitMax)
                ;
    }

    /**
     * ビルダインスタンスを生成する。
     *
     * @return builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * DcAccountLimitEntity オブジェクトを生成するための Builder クラス。
     *
     */
    public static class Builder {

        /** DC口座番号. */
        private DcBankNumber dcBankNumber;

        /** サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする. */
        private ServiceId serviceId;

        /** 1回あたりの発行設定可能限度額. */
        private Amount onceMintLimitMax;

        /** 1回あたりの償却設定可能限度額. */
        private Amount onceBurnLimitMax;

        /** 1回あたりの移転設定可能限度額. */
        private Amount onceTransferLimitMax;

        /** 1回あたりのチャージ設定可能限度額. */
        private Amount onceChargeLimitMax;

        /** 1回あたりのディスチャージ設定可能限度額. */
        private Amount onceDischargeLimitMax;

        /** 1日の発行累積額の設定可能限度額. */
        private Amount dailyMintLimitMax;

        /** 1日の償却累積額の設定可能限度額. */
        private Amount dailyBurnLimitMax;

        /** 1日の移転累積額の設定可能限度額. */
        private Amount dailyTransferLimitMax;

        /** 1日のチャージ累積額の設定可能限度額. */
        private Amount dailyChargeLimitMax;

        /** 1日のディスチャージ累積額の設定可能限度額. */
        private Amount dailyDischargeLimitMax;

        /** 1日の累積限度額の設定可能限度額. */
        private Amount dailyCumulativeLimitMax;

        private Builder() {
            // Do nothing.
        }

        /**
         * Build entity.
         *
         * @return the DcAccountLimitEntity object
         */
        public DcAccountLimitEntity build() {
            return new DcAccountLimitEntity(
                    this.dcBankNumber, 
                    this.serviceId, 
                    this.onceMintLimitMax, 
                    this.onceBurnLimitMax, 
                    this.onceTransferLimitMax, 
                    this.onceChargeLimitMax, 
                    this.onceDischargeLimitMax, 
                    this.dailyMintLimitMax, 
                    this.dailyBurnLimitMax, 
                    this.dailyTransferLimitMax, 
                    this.dailyChargeLimitMax, 
                    this.dailyDischargeLimitMax, 
                    this.dailyCumulativeLimitMax
            );
        }

        /**
         * Set dcBankNumber.
         *
         * @param dcBankNumber DC口座番号
         * @return this builder
         */
        public Builder dcBankNumber(DcBankNumber dcBankNumber) {
            this.dcBankNumber = dcBankNumber;
            return this;
        }

        /**
         * Set serviceId.
         *
         * @param serviceId サービスID : マルチテナント利用時にひとつの BPMServer に複数の事業者を扱うための仕組み。
通常の値は '0' とする
         * @return this builder
         */
        public Builder serviceId(ServiceId serviceId) {
            this.serviceId = serviceId;
            return this;
        }

        /**
         * Set onceMintLimitMax.
         *
         * @param onceMintLimitMax 1回あたりの発行設定可能限度額
         * @return this builder
         */
        public Builder onceMintLimitMax(Amount onceMintLimitMax) {
            this.onceMintLimitMax = onceMintLimitMax;
            return this;
        }

        /**
         * Set onceBurnLimitMax.
         *
         * @param onceBurnLimitMax 1回あたりの償却設定可能限度額
         * @return this builder
         */
        public Builder onceBurnLimitMax(Amount onceBurnLimitMax) {
            this.onceBurnLimitMax = onceBurnLimitMax;
            return this;
        }

        /**
         * Set onceTransferLimitMax.
         *
         * @param onceTransferLimitMax 1回あたりの移転設定可能限度額
         * @return this builder
         */
        public Builder onceTransferLimitMax(Amount onceTransferLimitMax) {
            this.onceTransferLimitMax = onceTransferLimitMax;
            return this;
        }

        /**
         * Set onceChargeLimitMax.
         *
         * @param onceChargeLimitMax 1回あたりのチャージ設定可能限度額
         * @return this builder
         */
        public Builder onceChargeLimitMax(Amount onceChargeLimitMax) {
            this.onceChargeLimitMax = onceChargeLimitMax;
            return this;
        }

        /**
         * Set onceDischargeLimitMax.
         *
         * @param onceDischargeLimitMax 1回あたりのディスチャージ設定可能限度額
         * @return this builder
         */
        public Builder onceDischargeLimitMax(Amount onceDischargeLimitMax) {
            this.onceDischargeLimitMax = onceDischargeLimitMax;
            return this;
        }

        /**
         * Set dailyMintLimitMax.
         *
         * @param dailyMintLimitMax 1日の発行累積額の設定可能限度額
         * @return this builder
         */
        public Builder dailyMintLimitMax(Amount dailyMintLimitMax) {
            this.dailyMintLimitMax = dailyMintLimitMax;
            return this;
        }

        /**
         * Set dailyBurnLimitMax.
         *
         * @param dailyBurnLimitMax 1日の償却累積額の設定可能限度額
         * @return this builder
         */
        public Builder dailyBurnLimitMax(Amount dailyBurnLimitMax) {
            this.dailyBurnLimitMax = dailyBurnLimitMax;
            return this;
        }

        /**
         * Set dailyTransferLimitMax.
         *
         * @param dailyTransferLimitMax 1日の移転累積額の設定可能限度額
         * @return this builder
         */
        public Builder dailyTransferLimitMax(Amount dailyTransferLimitMax) {
            this.dailyTransferLimitMax = dailyTransferLimitMax;
            return this;
        }

        /**
         * Set dailyChargeLimitMax.
         *
         * @param dailyChargeLimitMax 1日のチャージ累積額の設定可能限度額
         * @return this builder
         */
        public Builder dailyChargeLimitMax(Amount dailyChargeLimitMax) {
            this.dailyChargeLimitMax = dailyChargeLimitMax;
            return this;
        }

        /**
         * Set dailyDischargeLimitMax.
         *
         * @param dailyDischargeLimitMax 1日のディスチャージ累積額の設定可能限度額
         * @return this builder
         */
        public Builder dailyDischargeLimitMax(Amount dailyDischargeLimitMax) {
            this.dailyDischargeLimitMax = dailyDischargeLimitMax;
            return this;
        }

        /**
         * Set dailyCumulativeLimitMax.
         *
         * @param dailyCumulativeLimitMax 1日の累積限度額の設定可能限度額
         * @return this builder
         */
        public Builder dailyCumulativeLimitMax(Amount dailyCumulativeLimitMax) {
            this.dailyCumulativeLimitMax = dailyCumulativeLimitMax;
            return this;
        }
    }
}
// @formatter:on
