group = 'com.decurret_dcp'
version = '1.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}

dependencies {
    // basic
    implementation 'org.springframework.boot:spring-boot-starter'

    // spring-starter-web なしで jackson を利用するための依存関係
    implementation 'org.springframework.boot:spring-boot-starter-json'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // actuator
    implementation 'org.springframework.boot:spring-boot-starter-actuator'


    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // postgresql/doma
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.seasar.doma.boot:doma-spring-boot-starter:2.1.0'
    annotationProcessor "org.seasar.doma:doma-processor:3.1.0"

    // aws
    implementation platform('software.amazon.awssdk:bom:2.17.10')
    implementation 'software.amazon.awssdk:sqs'
    implementation 'software.amazon.awssdk:sts'
    implementation 'software.amazon.awssdk:secretsmanager'

    // ランダム文字列生成に利用
    implementation 'org.apache.commons:commons-text:1.11.0'
    // キャメルケースの文字列をスネークケースに変換するために利用している
    implementation 'com.google.guava:guava:31.1-jre'
    // Http通信
    implementation 'org.apache.httpcomponents.client5:httpclient5'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // spock
    testImplementation 'org.codehaus.groovy:groovy:3.0.24'
    testImplementation platform("org.spockframework:spock-bom:2.4-M1-groovy-3.0")
    testImplementation 'org.spockframework:spock-core'
    testImplementation 'org.spockframework:spock-spring'
    testImplementation 'com.athaydes:spock-reports:2.5.0-groovy-3.0'

    //adhoc
    testImplementation 'org.codehaus.groovy:groovy-sql:3.0.14'
    testImplementation 'org.testcontainers:testcontainers'
    testImplementation 'org.testcontainers:spock'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.apache.httpcomponents.client5:httpclient5'
    testImplementation "org.wiremock:wiremock-jetty12:3.12.1"

    implementation 'commons-codec:commons-codec:1.18.0'
}

tasks.compileJava {
    options.compilerArgs = [
            '-Adoma.dao.subpackage=impl',
            '-Adoma.dao.suffix=Impl',
            '-parameters'
    ]
}

tasks.named('test') {
    useJUnitPlatform()
    testLogging {
        events "STARTED", "PASSED", "FAILED", "SKIPPED"
        displayGranularity = 4
    }
    jvmArgs '--add-opens', 'java.base/java.lang=ALL-UNNAMED'
}

jacoco {
    toolVersion = "0.8.13"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation = layout.buildDirectory.dir("jacocoHtml")
    }
}
test.finalizedBy jacocoTestReport

eclipse {
    classpath {
        file {
            whenMerged { classpath ->
                classpath.entries.removeAll { it.path == ".apt_generated" }
            }
            withXml { provider ->
                def node = provider.asNode()
                // specify output path for .apt_generated
                node.appendNode("classpathentry", [kind: "src", output: "bin/main", path: ".apt_generated"])
            }
        }

        downloadJavadoc = true
        downloadSources = true
    }
}

idea {
    module {
        downloadJavadoc = true
        downloadSources = true
    }
}
