package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.service;

import java.math.BigInteger;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.core.transaction.config.TransactionTrackerProperty;
import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.AfterBalanceEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.ValidatorApi;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.command.ValidatorGetAccountCommand;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.contract.validator.result.ValidatorGetAccountResult;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.ClientEntityRepository;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.TransactionRepository;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.service.ValidatorFilter;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionAfterBalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.TransactionEntities;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionForceBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionIssueVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionMintMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionRedeemVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionSyncBizZoneBalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionTransferMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.atomic.ZoneType;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Service
@Slf4j
@Transactional
public class RegisterTransaction {

    private final ValidatorFilter validatorFilter;

    private final TransactionRepository transactionRepository;

    private final ClientEntityRepository clientEntityRepository;

    private final ValidatorApi validatorApi;

    @Autowired
    private TransactionTrackerProperty transactionTrackerProperty;

    private record ValidatorExists(boolean fromExist, boolean toExist) {
        // Do other codes.
    }

    /**
     * DCJPY 発行に関する取引履歴を登録する。
     *
     * @param message DCJPY 発行メッセージ
     */
    public void registerTransaction(TransactionMintMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * DCJPY 償却に関する取引履歴を登録する。
     *
     * @param message DCJPY 償却メッセージ
     */
    public void registerTransaction(TransactionBurnMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * Transfer イベントに関する取引履歴を登録する。
     *
     * @param message Transfer イベントメッセージ
     */
    public void registerTransaction(TransactionTransferMessage message) {
        switch (message.transactionType()) {
            case TRANSFER -> this.registerTransactionForTransfer(message);
            case CHARGE -> this.registerTransactionForCharge(message);
            case DISCHARGE -> this.registerTransactionForDischarge(message);
        }
    }

    /**
     * DCJPY 移転に関する取引履歴を登録する。(自身のDLT)
     *
     * @param message DCJPY 移転メッセージ
     */
    void registerTransactionForTransfer(TransactionTransferMessage message) {
        ValidatorExists validatorExists = this.existsValidators(message.fromValidatorId, message.toValidatorId);
        TransactionEntities fromTransactions = message.createTransferTransactionEntitiesFromAccount();
        TransactionEntities toTransactions = message.createTransferTransactionEntitiesToAccount();

        // from取引履歴テーブル登録
        if (validatorExists.fromExist) {
            this.transactionRepository.resisterTransferTransaction(fromTransactions);
            logTransaction(fromTransactions.transaction, "from ");
        }

        if (validatorExists.toExist) {
            // to取引履歴テーブル登録
            this.transactionRepository.resisterTransferTransaction(toTransactions);
            logTransaction(toTransactions.transaction, "to ");
        }
    }

    private ValidatorExists existsValidators(ValidatorId from, ValidatorId to) {
        boolean fromExist = this.validatorFilter.acceptable(from);
        boolean toExist = this.validatorFilter.acceptable(to);

        return new ValidatorExists(fromExist, toExist);
    }

    /**
     * FinZone にて、DCJPY チャージに関する取引履歴を登録する。
     *
     * @param message DCJPY チャージメッセージ
     */
    void registerTransactionForCharge(TransactionTransferMessage message) {
        TransactionEntity fromTransaction = message.createChargeTransactionFromAccount();
        TransactionEntity toTransaction = message.createChargeTransactionToAccount();

        // from取引履歴テーブル登録
        this.transactionRepository.registerTransaction(fromTransaction);
        // to取引履歴テーブル登録
        this.transactionRepository.registerTransaction(toTransaction);

        logTransaction(fromTransaction, "from ");
        logTransaction(toTransaction, "to ");
    }

    /**
     * FinZone にて、DCJPY ディスチャージに関する取引履歴を登録する。
     *
     * @param message DCJPY ディスチャージメッセージ
     */
    void registerTransactionForDischarge(TransactionTransferMessage message) {
        TransactionEntity fromTransaction = message.createDisChargeTransactionFromAccount();
        TransactionEntity toTransaction = message.createDisChargeTransactionToAccount();

        // from取引履歴テーブル登録
        this.transactionRepository.registerTransaction(fromTransaction);
        // to取引履歴テーブル登録
        this.transactionRepository.registerTransaction(toTransaction);

        logTransaction(fromTransaction, "from ");
        logTransaction(toTransaction, "to ");
    }

    /**
     * BizZone にて、DCJPY チャージ (BZ発行) に関する取引履歴を登録する。
     *
     * @param message DCJPY BZ 発行メッセージ
     */
    public void registerTransaction(TransactionIssueVoucherMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * BizZone にて、DCJPY ディスチャージ (BZ償却) に関する取引履歴を登録する。
     *
     * @param message DCJPY BZ 償却メッセージ
     */
    public void registerTransaction(TransactionRedeemVoucherMessage message) {
        TransactionEntity transaction = message.toTransaction();
        this.transactionRepository.registerTransaction(transaction);

        logTransaction(transaction);
    }

    /**
     * FinZone にて、BizZone の DCJPY 移転に関する取引履歴を登録する。
     *
     * @param message DCJPY 残高同期メッセージ
     */
    public void registerTransaction(TransactionSyncBizZoneBalanceMessage message) {
        ValidatorExists validatorExists = this.existsValidators(message.fromValidatorId, message.toValidatorId);
        TransactionEntity fromTransaction = message.createSyncBizBalanceTransactionFromAccount();
        TransactionEntity toTransaction = message.createSyncBizBalanceTransactionToAccount();

        // from取引履歴テーブル登録
        if (validatorExists.fromExist) {
            this.transactionRepository.registerTransaction(fromTransaction);
            logTransaction(fromTransaction, "from ");
        }

        // to取引履歴テーブル登録
        if (validatorExists.toExist) {
            this.transactionRepository.registerTransaction(toTransaction);
            logTransaction(toTransaction, "to ");
        }
    }

    /**
     * FinZone にて、強制償却に関する取引履歴を登録する。
     *
     * @param message DCJPY 残高同期メッセージ
     */
    public void registerTransaction(TransactionForceBurnMessage message) {

        // バリデータのゾーンを取得する
        ZoneId zoneId = this.clientEntityRepository.findZoneIdByEntityId(message.validatorId.toEntityId());

        // アカウント名を取得
        ValidatorGetAccountCommand command = ValidatorGetAccountCommand.builder()
                .zoneId(zoneId)
                .validatorId(message.validatorId)
                .accountId(message.accountId)
                .build();
        Either<ValidatorGetAccountResult> either = this.validatorApi.getAccount(command);
        if (either.isSuccess() == false) {
            log.error("Failed to get account. command : {}, response {}", command, either.failure());
        }

        ValidatorGetAccountResult accountResult = either.isSuccess() ? either.success() : null;
        List<TransactionEntity> transactions = message.toTransactions(accountResult);

        transactions.forEach(entity -> {
            this.transactionRepository.registerTransaction(entity);
            logTransaction(entity);
        });
    }

    /**
     * DCJPY 取引履歴の取引後総残高を更新する。
     *
     * @param message DCJPY 総残高同期メッセージ
     */
    public void registerTransaction(TransactionAfterBalanceMessage message) {
        // 環境変数から、BZ側のイベントかどうか判断する。BZ側のイベントだったら以降の処理を行わない。
        String zoneType = transactionTrackerProperty.zoneType;
        if(zoneType.equals(ZoneType.BUSINESS_ZONE.getValue())){
            return;
        }

        // 取引レコードの登録が済んでいない場合に備え、取得できなかった場合は2秒待ってから再度取得することを3回繰り返す
        boolean isSuccess = false;
        List<TransactionEntity> transactions = null;
        int retryCount = 0;

        while(retryCount < 3) {
            // トランザクションハッシュに紐づく取引レコードを取得
            transactions = this.transactionRepository.selectByTransactionHash(message.transactionHash);
            if(!transactions.isEmpty()) {
                isSuccess = true;
                break;
            }

            SleepUtil.sleepSilently(2000L);
            retryCount++;
        }

        if(!isSuccess){
            log.error("Failed to get transaction. transactionHash : {}", message.transactionHash);
            return;
        }

        // 取引履歴種別に応じて総残高を計算する
        for(TransactionEntity transaction : transactions) {
            TransactionEntity entity = null;

            switch (transaction.transactionType) {
                case MINT:
                case BURN:
                case CHARGE:
                case DISCHARGE:
                case FORCE_BURN:
                    entity = totalBalance(transaction, message.fromAfterBalance);
                    break;

                case TRANSFER :
                    // from側の総残高を算出
                    if (transaction.amount.getValue().compareTo(BigInteger.ZERO) < 0) {
                        entity = totalBalance(transaction, message.fromAfterBalance);
                    // to側の総残高を算出
                    } else {
                        entity = totalBalance(transaction, message.toAfterBalance);
                    }
                    break;
            }

            if(entity != null) {
                // 取引履歴の取引後総残高を更新する
                this.transactionRepository.updateTransaction(entity);
                logTransaction(entity);
            }
        }
    }


    private static void logTransaction(TransactionEntity transaction) {
        logTransaction(transaction, "");
    }

    private static void logTransaction(TransactionEntity transaction, String transactionOption) {
        log.info("Save {}transaction. transactionHash : {}, "
                         + "accountId : {}, zoneId : {}, transactionType : {}, amount : {}",
                 transactionOption, transaction.transactionHash.getValue(), transaction.accountId.getValue(),
                 transaction.zoneId.getValue(), transaction.transactionType.getValue(), transaction.amount.getValue());
    }

    private static TransactionEntity totalBalance(TransactionEntity entity, List<AfterBalanceEvent.AfterBalance> afterBalances){
        Balance totalBalance = Balance.of(
                afterBalances.stream()
                        .map(afterBalance -> afterBalance.balance.value)
                        .reduce(BigInteger.ZERO, BigInteger::add)
        );

        return TransactionEntity.builder()
                .transactionId(entity.transactionId)
                .transactionHash(entity.transactionHash)
                .accountId(entity.accountId)
                .accountName(entity.accountName)
                .validatorId(entity.validatorId)
                .zoneId(entity.zoneId)
                .transactedAt(entity.transactedAt)
                .transactionType(entity.transactionType)
                .amount(entity.amount)
                .postBalance(entity.postBalance)
                .postFzBalance(totalBalance)
                .otherAccountId(entity.otherAccountId)
                .otherAccountName(entity.otherAccountName)
                .otherZoneId(entity.otherZoneId)
                .build();
    }
}
