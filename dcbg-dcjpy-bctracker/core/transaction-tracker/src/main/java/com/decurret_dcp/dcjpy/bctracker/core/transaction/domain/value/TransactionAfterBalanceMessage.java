package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value;

import java.util.Collections;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.AfterBalanceEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.AfterBalanceEvent.AfterBalance;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 取引後の全てのゾーンの総残高を取引履歴に保存するためのメッセージ
 */
@ToString
@EqualsAndHashCode(callSuper = false)
@Builder(access = AccessLevel.PRIVATE)
public class TransactionAfterBalanceMessage implements TransactionMessage {

    /** トランザクションハッシュ. */
    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimestamp;

    public final ValidatorId validatorId;

    public final List<AfterBalance> fromAfterBalance;

    public final List<AfterBalance> toAfterBalance;

    @Override
    public List<ValidatorId> validatorIds() {
        return Collections.singletonList(this.validatorId);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.AFTER_BALANCE;
    }

    public static TransactionAfterBalanceMessage create(AfterBalanceEvent event) {
        return TransactionAfterBalanceMessage.builder()
                .transactionHash(event.transactionHash)
                .blockTimestamp(event.blockTimeStamp)
                .validatorId(event.validatorId)
                .fromAfterBalance(event.fromAfterBalance)
                .toAfterBalance(event.toAfterBalance)
                .build();
    }
}
