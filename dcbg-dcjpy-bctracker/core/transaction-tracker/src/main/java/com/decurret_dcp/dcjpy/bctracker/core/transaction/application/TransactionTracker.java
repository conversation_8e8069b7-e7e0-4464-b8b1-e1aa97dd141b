package com.decurret_dcp.dcjpy.bctracker.core.transaction.application;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.decurret_dcp.dcjpy.bctracker.base.application.BaseTracker;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.service.ValidatorFilter;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.service.RegisterTransaction;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionAfterBalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionForceBurnMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionIssueVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionMintMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionRedeemVoucherMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionSyncBizZoneBalanceMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionMessage;
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionTransferMessage;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * アカウント残高が変動したコントラクトのイベントを取引履歴に反映するトラッカー.
 */
@Slf4j
@RequiredArgsConstructor
@Transactional
@Component
public class TransactionTracker implements BaseTracker<TransactionMessage> {

    private final ValidatorFilter validatorFilter;

    private final RegisterTransaction registerTransaction;

    @Override
    public TransactionMessage acceptable(BCEvent<?> event) {
        TransactionMessage message = TransactionMessage.create(event.to());
        if (message == null) {
            return null;
        }

        boolean acceptable = this.validatorFilter.acceptable(message.validatorIds());
        if (acceptable == false) {
            return null;
        }

        return message;
    }

    @Override
    public boolean onMessage(TransactionMessage message) {
        switch (message.eventType()) {
            case MINT -> this.registerTransaction.registerTransaction((TransactionMintMessage) message);
            case BURN -> this.registerTransaction.registerTransaction((TransactionBurnMessage) message);
            case TRANSFER -> this.registerTransaction.registerTransaction((TransactionTransferMessage) message);
            case SYNC_BUSINESS_ZONE_BALANCE ->
                    this.registerTransaction.registerTransaction((TransactionSyncBizZoneBalanceMessage) message);
            case ISSUE_VOUCHER ->
                    this.registerTransaction.registerTransaction((TransactionIssueVoucherMessage) message);
            case REDEEM_VOUCHER ->
                    this.registerTransaction.registerTransaction((TransactionRedeemVoucherMessage) message);
            case FORCE_BURN -> this.registerTransaction.registerTransaction((TransactionForceBurnMessage) message);
            case AFTER_BALANCE -> this.registerTransaction.registerTransaction((TransactionAfterBalanceMessage) message);
        }

        return true;
    }
}
