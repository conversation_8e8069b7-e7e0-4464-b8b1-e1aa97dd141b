package com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * ゾーン種別を表す。
 *
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum ZoneType {

    /** フィナンシャルゾーン */
    FINANCIAL_ZONE("financial_zone"),

    /** ビジネスゾーン */
    BUSINESS_ZONE("business_zone");

    private static final Map<String, ZoneType> OBJECT_MAP = Arrays.stream(ZoneType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private ZoneType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static ZoneType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
