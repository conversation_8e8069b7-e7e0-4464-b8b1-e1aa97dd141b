package com.decurret_dcp.dcjpy.bctracker.core.transaction.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.AllArgsConstructor;
import lombok.ToString;

/**
 * TransactionTrackerのプロパティー情報.
 */
@ConfigurationProperties(prefix = "bctracker.transaction")
@AllArgsConstructor
@ToString
public class TransactionTrackerProperty {

    /** TransactionTracker が動作するZoneの種別を指定する */
    public final String zoneType;
}
