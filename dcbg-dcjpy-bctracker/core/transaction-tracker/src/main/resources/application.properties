# SpringBoot のバナーを表示しないようにする
spring.main.banner-mode=off

# ログのフォーマット
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(\\(%32.32X{traceId}\\)){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx

##########################################################################
# DB
##########################################################################
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=jdbc:postgresql://${DB_BASE:localhost}:${DB_PORT:5432}/${DB_NAME:postgres}
spring.datasource.username=${DB_USER:postgres}
spring.datasource.password=${DB_PASS:postgres}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.maximum-pool-size=${DB_MAXIMUM_POOL_SIZE:50}
spring.datasource.hikari.minimum-idle=${DB_MINIMUM_IDLE:10}
spring.datasource.hikari.connection-timeout=${DB_CONNECTION_TIMEOUT:30000}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIMEOUT:600000}

##########################################################################
# JACKSON
##########################################################################
# JSON のパラメータ名を snake_case として扱う
spring.jackson.property-naming-strategy=SNAKE_CASE
# JSON の日時形式を標準形式として扱う
spring.jackson.serialization.write-dates-as-timestamps=false

##########################################################################
# BCTracker 共通機能 (SQS)
##########################################################################
# 扱うイベントが格納されるキュー名。
bctracker.base.sqs.queue-name=${PUSH_NOTIFICATION_SQS_QUEUE_NAME:dcjpy_bctracker_queue_transaction.fifo}
# 可視性タイムアウト時間 (秒)
bctracker.base.sqs.visibility-timeout=${BASE_VISIBILITY_TIMEOUT:5}
# ポーリングメッセージ待機時間 (秒)
bctracker.base.sqs.wait-time-seconds=${BASE_WAIT_TIME_SECONDS:1}
# ローカル環境への接続先
bctracker.base.sqs.local-endpoint=${LOCAL_STACK_ENDPOINT:http://localhost:14566}

##########################################################################
# BCClientの設定
##########################################################################
bctracker.base.bcclient.base-url=${BCCLIENT_APP_BASE_URL:http://localhost:8081}
# BCクライアントに対するHTTPコネクション数の最大値
bctracker.base.bcclient.http-connection-max-per-route=${BCCLIENT_HTTP_CONNECTION_MAX_PER_ROUTE:50}
# 全HTTPコネクション数の最大値（現状はコネクション先がBCクライアントしか存在しないためHTTP_CONNECTION_MAX_PER_ROUTEと同値を設定）
bctracker.base.bcclient.http-connection-max-total=${BCCLIENT_HTTP_CONNECTION_MAX_TOTAL:50}
# BCクライアントからのレスポンスのタイムアウト値(ミリ秒)
bctracker.base.bcclient.read-timeout-millisecond=${BCCLIENT_READ_TIMEOUT_MILLISEC:20000}

##########################################################################
# Zoneの設定
##########################################################################
# TransactionTracker が動作するZoneの種別を指定する (financial_zone, business_zone)
bctracker.transaction.zone_type=${ZONE_TYPE:financial_zone}