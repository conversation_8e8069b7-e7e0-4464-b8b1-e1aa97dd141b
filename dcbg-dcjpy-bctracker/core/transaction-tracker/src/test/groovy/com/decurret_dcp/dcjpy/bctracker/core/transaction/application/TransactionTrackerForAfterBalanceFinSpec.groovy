package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.AfterBalanceEvent
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionAfterBalanceMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification
import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil

@Testcontainers
@SpringBootTest()
class TransactionTrackerForAfterBalanceFinSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockitoBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        registry.add("bctracker.transaction.zone_type"){"financial_zone"}
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * AfterBalanceイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return AfterBalanceイベントのnonIndexValuesの値
     */
    private static JsonNode getAfterBalanceIndexValues() {
        String indexValues = """
            {
                "issuerId" : [50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * AfterBalanceイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return MintイベントのindexValuesの値
     */
    private static JsonNode getAfterBalanceNoneIndexValues() {
        String testData = """
            {
                "fromAfterBalance":[
                {
                    "zoneId":"3000",
                    "balance":"1000"
                },
                {
                    "zoneId":"3001",
                    "balance":"2000"
                },
                {
                    "zoneId":"3002",
                    "balance":"3000"
                }
                ],
                "toAfterBalance":[
                {
                    "zoneId":"3000",
                    "balance":"1000"
                },
                {
                    "zoneId":"3001",
                    "balance":"1000"
                },
                {
                    "zoneId":"3002",
                    "balance":"1000"
                }
                ],
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        return objectMapper.readTree(testData)
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:MINT"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-************', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'mint',1000,1000,NULL,NULL,NULL,NULL)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()


        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 1
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-************")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("mint")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 1000
        transactionRecord.get(0).get("post_fz_balance") == 6000
        Objects.isNull(transactionRecord.get(0).get("other_account_id"))
        Objects.isNull(transactionRecord.get(0).get("other_account_name"))
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:BURN"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-********0002', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'burn',1000,1000,NULL,NULL,NULL,NULL)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 1
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0002")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("burn")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 1000
        transactionRecord.get(0).get("post_fz_balance") == 6000
        Objects.isNull(transactionRecord.get(0).get("other_account_id"))
        Objects.isNull(transactionRecord.get(0).get("other_account_name"))
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:CHARGE"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-********0003', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'charge',-1000,0,NULL,'0000-0000-0001','アカウントメイイチ',3001),
               ('********-0000-0000-0000-********0004', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3001, '2025/04/15 12:00:00', 'charge',1000,2000,NULL,'0000-0000-0001','アカウントメイイチ',3000)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 2
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0004")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3001
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("charge")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 2000
        transactionRecord.get(0).get("post_fz_balance") == 6000
        transactionRecord.get(0).get("other_account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("other_account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("other_zone_id") == 3000

        transactionRecord.get(1).get("transaction_id").equals("********-0000-0000-0000-********0003")
        transactionRecord.get(1).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de22")
        transactionRecord.get(1).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(1).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(1).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(1).get("zone_id") == 3000
        transactionRecord.get(1).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(1).get("transaction_type").equals("charge")
        transactionRecord.get(1).get("amount") == -1000
        transactionRecord.get(1).get("post_balance") == 0
        transactionRecord.get(1).get("post_fz_balance") == 6000
        transactionRecord.get(1).get("other_account_id").equals("0000-0000-0001")
        transactionRecord.get(1).get("other_account_name").equals("アカウントメイイチ")
        transactionRecord.get(1).get("other_zone_id") == 3001
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:DISCHARGE"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-********0005', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'discharge',1000,1000,NULL,'0000-0000-0001','アカウントメイイチ',3001),
               ('********-0000-0000-0000-********0006', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3001, '2025/04/15 12:00:00', 'discharge',-1000,0,NULL,'0000-0000-0001','アカウントメイイチ',3000)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 2
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0006")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3001
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("discharge")
        transactionRecord.get(0).get("amount") == -1000
        transactionRecord.get(0).get("post_balance") == 0
        transactionRecord.get(0).get("post_fz_balance") == 6000
        transactionRecord.get(0).get("other_account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("other_account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("other_zone_id") == 3000

        transactionRecord.get(1).get("transaction_id").equals("********-0000-0000-0000-********0005")
        transactionRecord.get(1).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de23")
        transactionRecord.get(1).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(1).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(1).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(1).get("zone_id") == 3000
        transactionRecord.get(1).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(1).get("transaction_type").equals("discharge")
        transactionRecord.get(1).get("amount") == 1000
        transactionRecord.get(1).get("post_balance") == 1000
        transactionRecord.get(1).get("post_fz_balance") == 6000
        transactionRecord.get(1).get("other_account_id").equals("0000-0000-0001")
        transactionRecord.get(1).get("other_account_name").equals("アカウントメイイチ")
        transactionRecord.get(1).get("other_zone_id") == 3001
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:FORCE_BURN"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-********0007', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de24', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'force_burn',3000,0,NULL,NULL,NULL,NULL)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de24"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de24';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 1
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0007")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de24")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("force_burn")
        transactionRecord.get(0).get("amount") == 3000
        transactionRecord.get(0).get("post_balance") == 0
        transactionRecord.get(0).get("post_fz_balance") == 6000
        Objects.isNull(transactionRecord.get(0).get("other_account_id"))
        Objects.isNull(transactionRecord.get(0).get("other_account_name"))
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_TransactionType:TRANSFER"() {
        setup:
        // 取引履歴テーブルをインサート
        sql.execute("""
        INSERT INTO transaction
        (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
        VALUES ('********-0000-0000-0000-********0008', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'transfer',-1000,0,NULL,'0000-0000-0002','アカウントメイニ',NULL),
               ('********-0000-0000-0000-********0009', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25', '0000-0000-0002', 'アカウントメイニ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'transfer',1000,2000,NULL,'0000-0000-0001','アカウントメイイチ',NULL)
        """)

        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 2

        transactionRecord.get(1).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25")
        transactionRecord.get(1).get("transaction_id").equals("********-0000-0000-0000-********0008")
        transactionRecord.get(1).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(1).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(1).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(1).get("zone_id") == 3000
        transactionRecord.get(1).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(1).get("transaction_type").equals("transfer")
        transactionRecord.get(1).get("amount") == -1000
        transactionRecord.get(1).get("post_balance") == 0
        transactionRecord.get(1).get("post_fz_balance") == 6000
        transactionRecord.get(1).get("other_account_id").equals("0000-0000-0002")
        transactionRecord.get(1).get("other_account_name").equals("アカウントメイニ")
        Objects.isNull(transactionRecord.get(1).get("other_zone_id"))

        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0009")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de25")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0002")
        transactionRecord.get(0).get("account_name").equals("アカウントメイニ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("transfer")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 2000
        transactionRecord.get(0).get("post_fz_balance") == 3000
        transactionRecord.get(0).get("other_account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("other_account_name").equals("アカウントメイイチ")
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_transactionレコードが存在しない"() {
        setup:
        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()


        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x4508d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x4508d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 0
    }

    def "testOnMessage_AfterBalanceイベントが発生した場合_transactionレコードが実行中に挿入される"() {
        setup:
        JsonNode indexValues = getAfterBalanceIndexValues()
        JsonNode nonIndexValues = getAfterBalanceNoneIndexValues()


        def testEvent = BCEvent.<Message> builder()
                .name("AfterBalance")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de26"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        AfterBalanceEvent afterBalanceEvent = AfterBalanceEvent.create(testEvent)
        TransactionAfterBalanceMessage afterBalanceMessage = TransactionAfterBalanceMessage.create(afterBalanceEvent)

        // 非同期で 2 秒後に transaction レコードを挿入
        Thread.startDaemon {
            SleepUtil.sleepSilently(2000L)
            sql.execute("""
                INSERT INTO transaction
                (transaction_id, transaction_hash, account_id, account_name, validator_id, zone_id, transacted_at, transaction_type,amount,post_balance,post_fz_balance,other_account_id,other_account_name,other_zone_id)
                VALUES ('********-0000-0000-0000-********0010', '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de26', '0000-0000-0001', 'アカウントメイイチ', '80bOvB8L3VxYSCM5QWd1WpSNenGaGFba', 3000, '2025/04/15 12:00:00', 'mint',1000,1000,NULL,NULL,NULL,NULL)
            """)
        }

        when:
        def result = this.transactionTracker.onMessage(afterBalanceMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de26';
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 1
        transactionRecord.get(0).get("transaction_id").equals("********-0000-0000-0000-********0010")
        transactionRecord.get(0).get("transaction_hash").equals("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de26")
        transactionRecord.get(0).get("account_id").equals("0000-0000-0001")
        transactionRecord.get(0).get("account_name").equals("アカウントメイイチ")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        transactionRecord.get(0).get("transacted_at").toString().equals("2025-04-15 12:00:00.0")
        transactionRecord.get(0).get("transaction_type").equals("mint")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 1000
        transactionRecord.get(0).get("post_fz_balance") == 6000
        Objects.isNull(transactionRecord.get(0).get("other_account_id"))
        Objects.isNull(transactionRecord.get(0).get("other_account_name"))
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """
        )
    }
}
