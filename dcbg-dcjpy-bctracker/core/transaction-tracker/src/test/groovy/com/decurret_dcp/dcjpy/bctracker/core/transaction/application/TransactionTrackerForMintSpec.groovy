package com.decurret_dcp.dcjpy.bctracker.core.transaction.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.MintEvent
import com.decurret_dcp.dcjpy.bctracker.core.transaction.domain.value.TransactionMintMessage
import com.decurret_dcp.dcjpy.bctracker.core.transaction.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Specification

@Testcontainers
@SpringBootTest()
class TransactionTrackerForMintSpec extends Specification {

    @Autowired
    TransactionTracker transactionTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockitoBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        sqsClient = CoreAdhocHelper.createSQS("dcjpy_bctracker_queue_transaction.fifo")
        objectMapper = new ObjectMapper()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * MintイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return MintイベントのnonIndexValuesの値
     */
    private static JsonNode getMintIndexValues() {
        String indexValues = """
            {
                "issuerId" : [50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * MintイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return MintイベントのindexValuesの値
     */
    private static JsonNode getMintNoneIndexValues() {
        String nonIndexValues = """
            {
                "zoneId":"3000",
                "amount": "1000",
                "balance": "2500",
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "accountName": "モックギンコウコウザ1",
                "validatorId": [56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "testAcceptable_MintイベントでvalidatorIdがclient entityテーブルに存在しない場合"() {
        setup:
        JsonNode indexValues = getMintIndexValues()
        String testData = """
            {
                "zoneId":"3000",
                "amount": "1000",
                "balance": "2500",
                "accountId": [54,48,98,79,118,67,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],
                "accountName": "モックギンコウコウザ1",
                "validatorId": [56,0,0,0,0,0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
            }
        """
        JsonNode nonIndexValues = objectMapper.readTree(testData)

        def testEvent = BCEvent.<Message> builder()
                .name("Mint")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "testAcceptable_Mintイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getMintIndexValues()
        JsonNode nonIndexValues = getMintNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Mint")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de20"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        when:
        def result = this.transactionTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.MINT
        result.validatorIds().size() == 1
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
    }

    def "testOnMessage_Mintイベントが発生した場合"() {
        setup:
        JsonNode indexValues = getMintIndexValues()
        JsonNode nonIndexValues = getMintNoneIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("Mint")
                .transactionHash(new TransactionHash("0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21"))
                .blockTimestamp(BlockTimeStamp.of(**********))
                .logIndex(0)
                .log("address: 0xeec918d74c746167564401103096d45bbd494b74")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .original(null)
                .build()

        MintEvent mintEvent = MintEvent.create(testEvent)
        TransactionMintMessage mintMessage = TransactionMintMessage.create(mintEvent)

        when:
        def result = this.transactionTracker.onMessage(mintMessage)
        def transactionRecord = sql.rows("""
            SELECT * FROM transaction WHERE transaction_hash = '0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46776f3144898e109b3f0de21';
        """)

        def memoRecord = sql.firstRow("""
            SELECT * FROM transaction_memo;
        """
        )

        def miscRecord = sql.firstRow("""
            SELECT * FROM transaction_misc;
        """)

        def senderRecord = sql.firstRow("""
            SELECT * FROM transaction_sender;
        """)

        then:
        result == true
        !Objects.isNull(transactionRecord)
        transactionRecord.size() == 1
        !Objects.isNull(transactionRecord.get(0).get("transaction_id"))
        transactionRecord.get(0).get("account_id").equals("60bOvC8L3VxYSCM5QWd1WpSNenGaGFbb")
        transactionRecord.get(0).get("account_name").equals("モックギンコウコウザ1")
        transactionRecord.get(0).get("validator_id").equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        transactionRecord.get(0).get("zone_id") == 3000
        !Objects.isNull(transactionRecord.get(0).get("transacted_at"))
        transactionRecord.get(0).get("transaction_type").equals("mint")
        transactionRecord.get(0).get("amount") == 1000
        transactionRecord.get(0).get("post_balance") == 2500
        Objects.isNull(transactionRecord.get(0).get("other_account_id"))
        Objects.isNull(transactionRecord.get(0).get("other_account_name"))
        Objects.isNull(transactionRecord.get(0).get("other_zone_id"))

        Objects.isNull(memoRecord)
        Objects.isNull(miscRecord)
        Objects.isNull(senderRecord)

        cleanup:
        sql.execute("""
            DELETE FROM transaction;
        """)
    }

}
