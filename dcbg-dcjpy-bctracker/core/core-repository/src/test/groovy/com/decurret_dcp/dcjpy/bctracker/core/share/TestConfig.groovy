package com.decurret_dcp.dcjpy.bctracker.core.share

import com.decurret_dcp.dcjpy.bctracker.base.adaptor.db.DomaTransactionSupport
import com.decurret_dcp.dcjpy.bctracker.base.domain.repository.TransactionSupport
import com.decurret_dcp.dcjpy.bctracker.core.share.config.BCClientProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration

@Configuration
@EnableAutoConfiguration
@EnableConfigurationProperties([BCClientProperty.class])
@ComponentScan(basePackages = "com.decurret_dcp.dcjpy.bctracker.core.share")
class TestConfig {
    @Bean
    TransactionSupport transactionSupport() {
        return new DomaTransactionSupport();
    }

    @Bean
    ObjectMapper objectMapper() {
        return new ObjectMapper()
    }

    @Bean
    RestTemplateBuilder restTemplateBuilder() {
        return new RestTemplateBuilder()
    }
}
