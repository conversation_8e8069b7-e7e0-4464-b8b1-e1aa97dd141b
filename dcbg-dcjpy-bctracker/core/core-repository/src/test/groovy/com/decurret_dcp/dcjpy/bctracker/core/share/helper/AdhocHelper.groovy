package com.decurret_dcp.dcjpy.bctracker.core.share.helper

import groovy.sql.Sql
import org.springframework.test.context.DynamicPropertyRegistry
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.shaded.com.fasterxml.jackson.databind.JsonNode
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper

class AdhocHelper {

    private static final int DB_POOL_CONNECTION_IDLE_SIZE = 2

    private static final ObjectMapper mapper = new ObjectMapper()

    private static dbPort

    private static DockerComposeContainer composeContainer

    private static connectionCount = 0

    static Sql sql

    static String getDbPort() { return dbPort }

    static {
        startContainer()
    }

    private static void startContainer() {
        // dbが起動した後にpostgresにアクセスできるように、Wait.forLogMessage()でアクセス可能になるログが出力されるまで待つ
        composeContainer = new DockerComposeContainer(new File("../docker-compose_cicd.yml"))
                .withExposedService("db", 5432)
                .waitingFor("db", Wait.forListeningPort())
                .waitingFor("db", Wait.forLogMessage(".*database system is ready to accept connections.*\\n", 1))

        composeContainer.start()
        dbPort = String.valueOf(composeContainer.getServicePort("db", 5432))
    }

    static Sql initAdhoc(DynamicPropertyRegistry registry) {
        var jdbcUrl = "***************************:${dbPort}/postgres"

        registry.add("spring.datasource.url", () -> jdbcUrl)

        // テストではコネクションを多く用意する必要はないので、少なめにする
        registry.add("spring.datasource.hikari.minimum-idle", () -> DB_POOL_CONNECTION_IDLE_SIZE)

        sql = Sql.newInstance(jdbcUrl, "postgres", "postgres", "org.postgresql.Driver")

        return sql
    }

    static void cleanupSpec() {
        sql.close()

        // テストクラスごとにSpringBootを起動しているにも関わらず、
        // なぜか以前のテストケースでのDBコネクションが残ったままなので、
        // デフォルトのDBコネクションプールのサイズを超えたら、コンテナを再起動する
        connectionCount += DB_POOL_CONNECTION_IDLE_SIZE
        if (connectionCount >= 100) {
            composeContainer.stop()
            connectionCount = 0

            startContainer()
        }
    }

    static void setupSql() {
        // TODO テスト用データの投入
    }

    static void cleanupSql() {
        // TODO 必要ならデリート用のSQLを作成
    }

    static JsonNode toJsonNode(String json) {
        return mapper.readTree(json)
    }
}
