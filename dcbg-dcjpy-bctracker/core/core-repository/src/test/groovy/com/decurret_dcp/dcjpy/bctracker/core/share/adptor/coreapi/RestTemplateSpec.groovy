package com.decurret_dcp.dcjpy.bctracker.core.share.adptor.coreapi

import com.decurret_dcp.dcjpy.bctracker.base.domain.service.TraceIdHolder
import com.decurret_dcp.dcjpy.bctracker.core.share.TestConfig
import com.decurret_dcp.dcjpy.bctracker.core.share.adaptor.bcclient.BCClientSupport
import com.decurret_dcp.dcjpy.bctracker.core.share.config.BCClientProperty
import com.decurret_dcp.dcjpy.bctracker.core.share.helper.AdhocHelper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus
import org.springframework.http.RequestEntity
import org.springframework.http.ResponseEntity
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.util.ReflectionTestUtils
import org.springframework.web.client.ResourceAccessException
import org.springframework.web.client.RestTemplate
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.*
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@SpringBootTest(
        classes = TestConfig.class,
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class RestTemplateSpec extends Specification {

    @Autowired
    BCClientProperty property

    @Shared
    WireMockServer wiremockIdp

    @Autowired
    BCClientSupport bCClientSupport

    static Sql sql

    static final String TARGET_URL = "/test"

    static final Integer MOCK_CORE_IDP_PORT = 18282

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = AdhocHelper.initAdhoc(registry)

        registry.add("bctracker.base.bcclient.read-timeout-millisecond", () -> 1000)
        registry.add("bctracker.base.bcclient.http-connection-max-per-route", () -> 50)
        registry.add("bctracker.base.bcclient.http-connection-max-total", () -> 50)
        registry.add("bctracker.base.bcclient.base-url", () -> "http://localhost:${MOCK_CORE_IDP_PORT}/oauth2/token")
    }

    def setupSpec() {
        wiremockIdp = new WireMockServer(options().port(MOCK_CORE_IDP_PORT))
        wiremockIdp.start()
    }

    def cleanupSpec() {
        wiremockIdp.stop()
    }


    def "bCClientSupportのrestTemplateフィールドに設定したTraceIdInterceptorが動くか確認_リクエストにtrace-idが設定されている"() {
        setup:
        final TRACE_ID = "traceId"
        TraceIdHolder.setTraceId(TRACE_ID)
        wiremockIdp.stubFor(get(urlEqualTo("/oauth2/token" + TARGET_URL)).withHeader("trace-id", matching(TRACE_ID))
                .willReturn(aResponse().withStatus(200)))
        RequestEntity<Void> request = RequestEntity.get(TARGET_URL).build()
        RestTemplate target = ReflectionTestUtils.getField(bCClientSupport, "restTemplate") as RestTemplate

        when:
        ResponseEntity<Void> response = target.exchange(request, Void)

        then:
        response.getStatusCode() == HttpStatus.OK
        TraceIdHolder.remove()
    }

    def "bCClientSupportのrestTemplateフィールドに設定したreadTimeoutが設定通りに動くか確認_レスポンスがreadTimeoutより長い場合_タイムアウトエラーになる"() {
        setup:
        wiremockIdp.stubFor(get(urlEqualTo("/oauth2/token" + TARGET_URL)).willReturn(
                aResponse().withFixedDelay((Integer) property.readTimeoutMillisecond + 1000)))
        RequestEntity<Void> request = RequestEntity.get(TARGET_URL).build()
        RestTemplate target = ReflectionTestUtils.getField(bCClientSupport, "restTemplate") as RestTemplate

        when:
        target.exchange(request, Void)

        then:
        thrown(ResourceAccessException)
    }
}
