package com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity;

import org.seasar.doma.Column;
import org.seasar.doma.Entity;
import org.seasar.doma.Id;
import org.seasar.doma.Table;

import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Amount;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;

import lombok.Builder;

/**
 * 取引履歴.
 */
@Entity(immutable = true)
@Table(name = "transaction")
@Builder(toBuilder = true)
public class TransactionEntity {

    /** トランザクションID */
    @Id
    @Column(name = "transaction_id")
    public final TransactionId transactionId;

    /** トランザクションハッシュ */
    @Column(name = "transaction_hash")
    public final TransactionHash transactionHash;

    /** アカウントID */
    @Column(name = "account_id")
    public final AccountId accountId;

    /** アカウント名 */
    @Column(name = "account_name")
    public final String accountName;

    /** バリデータID */
    @Column(name = "validator_id")
    public final ValidatorId validatorId;

    /** ゾーンID */
    @Column(name = "zone_id")
    public final ZoneId zoneId;

    /** 取引日時 */
    @Column(name = "transacted_at")
    public final AppTimeStamp transactedAt;

    /** 取引種別 */
    @Column(name = "transaction_type")
    public final TransactionType transactionType;

    /** 取引額 */
    @Column(name = "amount")
    public final Amount amount;

    /** 取引後残高 */
    @Column(name = "post_balance")
    public final Balance postBalance;

    /** 取引後FZ残高 */
    @Column(name = "post_fz_balance")
    public final Balance postFzBalance;

    /** 取引後全てのBZ総残高 */
    @Column(name = "post_bz_total_balance")
    public final Balance postBzTotalBalance;

    /** 取引先アカウントID */
    @Column(name = "other_account_id")
    public final AccountId otherAccountId;

    /** 取引先アカウント名 */
    @Column(name = "other_account_name")
    public final String otherAccountName;

    /** 取引先ゾーンID */
    @Column(name = "other_zone_id")
    public final ZoneId otherZoneId;
}
