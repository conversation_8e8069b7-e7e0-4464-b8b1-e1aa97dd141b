package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.coreapi;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception.ContractFailureException;
import com.decurret_dcp.dcjpy.bctracker.base.domain.repository.TransactionSupport;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.Either;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AdminId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.EntityType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.IssuerId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ProviderId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor.RestTemplateSupport;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.config.CoreApiApplicationProperty;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.service.CredentialStorage;
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.credential.Oauth2Secret;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.entity.ClientEntity;
import com.decurret_dcp.dcjpy.bctracker.core.share.domain.repository.ClientEntityRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CoreAdaptorSupport {

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newSingleThreadScheduledExecutor();

    private static final String UNEXPECTED_ERROR_OCCURRED_FROM_CONTRACT = "E1000";

    private final TransactionSupport transactionSupport;

    private final ClientEntityRepository clientEntityRepository;

    private final CredentialStorage credentialStorage;

    private final RestTemplate restTemplate;

    private final RestTemplate idpTemplate;

    private final Map<String, String> tokenMap;

    private final ObjectMapper objectMapper;

    public CoreAdaptorSupport(
            CoreApiApplicationProperty property, ObjectMapper objectMapper, RestTemplateSupport restSupport,
            TransactionSupport transactionSupport, ClientEntityRepository authClientRepository,
            CredentialStorage credentialStorage
    ) {
        this.transactionSupport = transactionSupport;
        this.clientEntityRepository = authClientRepository;
        this.credentialStorage = credentialStorage;
        this.restTemplate = restSupport
                .restTemplateBuilder()
                .requestFactory(HttpComponentsClientHttpRequestFactory.class)
                .rootUri(property.coreApi.baseUrl)
                .readTimeout(Duration.ofMillis(property.coreApi.readTimeoutMillisecond))
                .additionalInterceptors(TraceIdInterceptor.get())
                .build();

        this.idpTemplate = restSupport
                .restTemplateBuilder()
                .requestFactory(HttpComponentsClientHttpRequestFactory.class)
                .rootUri(property.coreApi.idpUrl)
                .readTimeout(Duration.ofMillis(property.coreApi.readTimeoutMillisecond))
                .build();

        this.tokenMap = new ConcurrentHashMap<>();
        this.objectMapper = objectMapper;
    }

    public RequestEntity.HeadersBuilder<?> initGet(ProviderId providerId, String path) {
        String token = this.fetchToken(EntityType.PROVIDER, providerId.toEntityId());
        return RequestEntity.get(path)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPost(ProviderId providerId, String path) {
        String token = this.fetchToken(EntityType.PROVIDER, providerId.toEntityId());
        return RequestEntity.post(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initGet(AdminId adminId, String path) {
        String token = this.fetchToken(EntityType.ADMIN, adminId.toEntityId());
        return RequestEntity.get(path)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPost(AdminId adminId, String path) {
        String token = this.fetchToken(EntityType.ADMIN, adminId.toEntityId());
        return RequestEntity.post(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initGet(ValidatorId validatorId, String path) {
        String token = this.fetchToken(EntityType.VALIDATOR, validatorId.toEntityId());
        return RequestEntity.get(path)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initGet(ValidatorId validatorId, URI uri) {
        String token = this.fetchToken(EntityType.VALIDATOR, validatorId.toEntityId());
        return RequestEntity.get(uri)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPost(ValidatorId validatorId, String path) {
        String token = this.fetchToken(EntityType.VALIDATOR, validatorId.toEntityId());
        return RequestEntity.post(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPut(ValidatorId validatorId, String path) {
        String token = this.fetchToken(EntityType.VALIDATOR, validatorId.toEntityId());
        return RequestEntity.put(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initDelete(ValidatorId validatorId, String path) {
        String token = this.fetchToken(EntityType.VALIDATOR, validatorId.toEntityId());
        return RequestEntity.delete(path)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initGet(IssuerId issuerId, String path) {
        String token = this.fetchToken(EntityType.ISSUER, issuerId.toEntityId());
        return RequestEntity.get(path)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPost(IssuerId issuerId, String path) {
        String token = this.fetchToken(EntityType.ISSUER, issuerId.toEntityId());
        return RequestEntity.post(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.BodyBuilder initPut(IssuerId issuerId, String path) {
        String token = this.fetchToken(EntityType.ISSUER, issuerId.toEntityId());
        return RequestEntity.put(path)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", token);
    }

    public RequestEntity.HeadersBuilder<?> initDelete(IssuerId issuerId, String path) {
        String token = this.fetchToken(EntityType.ISSUER, issuerId.toEntityId());
        return RequestEntity.delete(path)
                .header("Authorization", token);
    }

    public <REQUEST, RESPONSE> Either<RESPONSE> execute(RequestEntity<REQUEST> request, Class<RESPONSE> responseClass) {
        ResponseEntity<String> response = this.restTemplate.exchange(request, String.class);
        HttpStatusCode httpStatusCode = response.getStatusCode();

        if (httpStatusCode.is2xxSuccessful() == false) {
            try {
                CoreApiErrorResponse errorResponse = this.objectMapper.readValue(
                        response.getBody(), CoreApiErrorResponse.class);

                if (UNEXPECTED_ERROR_OCCURRED_FROM_CONTRACT.equals(errorResponse.errorCode)) {
                    throw ContractFailureException.failed(errorResponse.detail);
                }

                return Either.failure(errorResponse.errorCode, errorResponse.detail);
            } catch (JsonProcessingException jsonExc) {
                log.error("Failed to parse error response from core api. response :" + response, jsonExc);
                throw new RuntimeException(jsonExc);
            }
        }

        try {
            if (response.getBody() == null) {
                return Either.success(null);
            }

            RESPONSE responseBody = this.objectMapper.readValue(response.getBody(), responseClass);
            return Either.success(responseBody);
        } catch (JsonProcessingException jsonExc) {
            log.error("Failed to parse response from core api. response :" + response, jsonExc);
            throw new RuntimeException(jsonExc);
        }
    }

    private String fetchToken(EntityType entityType, EntityId entityId) {
        String token = tokenMap.get(entityId.getValue());
        // token の利用期限が切れていない場合はそのまま利用する
        if (token != null) {
            return token;
        }

        //ClientIdの取得
        ClientEntity clientEntity = this.transactionSupport.transaction(
                () -> clientEntityRepository.findClientEntityId(entityId)
        );
        //ClientSecretの取得
        Oauth2Secret clientSecret
                = this.credentialStorage.findClientSecret(EntityId.of(clientEntity.entityId.value));

        String authorizationHeader = encodeAuthorization(clientEntity, clientSecret);
        RequestEntity<String> request = RequestEntity.post("/")
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("Authorization", authorizationHeader)
                .acceptCharset(StandardCharsets.UTF_8)
                .body("grant_type=client_credentials");
        ResponseEntity<IdpAccessToken> response = this.idpTemplate.exchange(request, IdpAccessToken.class);

        if (response.getStatusCode().is2xxSuccessful() == false) {
            log.error("Failed to fetch token from core idp. response = {}", response);
            throw new RuntimeException("Failed to fetch token from core idp.");
        }

        IdpAccessToken idpToken = response.getBody();
        tokenMap.put(entityId.getValue(), idpToken.accessToken);

        // トークンの有効期限が切れる前に削除する
        long expiresIn = Math.max(1L, idpToken.expiresIn - 60L);
        Runnable deleteTask = () -> tokenMap.remove(entityId.getValue());
        EXECUTOR_SERVICE.schedule(deleteTask, expiresIn, TimeUnit.SECONDS);

        return idpToken.accessToken;
    }

    private static String encodeAuthorization(ClientEntity authClient, Oauth2Secret oauth2Secret) {
        byte[] bytes = (authClient.clientId.getValue() + ":" + oauth2Secret.clientSecret.getValue()).getBytes();
        String encoded = Base64.getEncoder().encodeToString(bytes);
        return "Basic " + encoded;
    }

    private static class IdpAccessToken {

        public String accessToken;

        public long expiresIn;
    }

    @ToString
    private static class CoreApiErrorResponse {

        public String message;

        public String detail;

        public String errorCode;
    }
}
