package com.decurret_dcp.dcjpy.bctracker.core.invokecore.adaptor;

import java.util.function.Supplier;

import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.ResponseErrorHandler;

import com.decurret_dcp.dcjpy.bctracker.core.invokecore.config.CoreApiApplicationProperty;

@Component
public class RestTemplateSupport {

    private static final ResponseErrorHandler ERROR_HANDLER = initErrorHandler();

    private final RestTemplateBuilder builder;

    public RestTemplateSupport(RestTemplateBuilder restBuilder, CoreApiApplicationProperty property) {
        Supplier<ClientHttpRequestFactory> factorySupplier = initFactorySupplier(property);
        this.builder = restBuilder
                .requestFactory(factorySupplier)
                .errorHandler(ERROR_HANDLER);
    }

    private static Supplier<ClientHttpRequestFactory> initFactorySupplier(CoreApiApplicationProperty property) {
        PoolingHttpClientConnectionManager poolManager = new PoolingHttpClientConnectionManager();
        poolManager.setMaxTotal(property.coreApi.httpConnection.maxTotal.intValue());
        poolManager.setDefaultMaxPerRoute(property.coreApi.httpConnection.maxPerRoute.intValue());

        CloseableHttpClient client = HttpClients.custom()
                .setConnectionManager(poolManager)
                .build();
        ClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(client);

        return () -> factory;
    }

    private static ResponseErrorHandler initErrorHandler() {
        return new DefaultResponseErrorHandler() {
            @Override
            public void handleError(org.springframework.http.client.ClientHttpResponse response) {
                // Don't throw Exception.
            }
        };
    }

    public RestTemplateBuilder restTemplateBuilder() {
        return this.builder;
    }
}
