package com.decurret_dcp.dcjpy.bctracker.core.invokecore.application

import com.decurret_dcp.dcjpy.bctracker.base.application.BCTrackerApplication
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.event.DischargeRequestedEvent
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception.InvokeCoreApiDischargeFailureException
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.exception.ResourceNotFoundException
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.domain.value.DischargeRequestedMessage
import com.decurret_dcp.dcjpy.bctracker.core.invokecore.helper.CoreAdhocHelper
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.tomakehurst.wiremock.WireMockServer
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.web.client.ResourceAccessException
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse
import static com.github.tomakehurst.wiremock.client.WireMock.badRequest
import static com.github.tomakehurst.wiremock.client.WireMock.okJson
import static com.github.tomakehurst.wiremock.client.WireMock.post
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options

@Testcontainers
@SpringBootTest()
class InvokeCoreTrackerForDischargeSpec extends Specification {

    @Autowired
    InvokeCoreTracker invokeCoreTracker

    // Mockにしないと、bcTrackerApplicationが自動的に実行され、テストコードが実行されない。
    @MockitoBean
    BCTrackerApplication bcTrackerApplication

    static ObjectMapper objectMapper

    static Sql sql

    static SqsClient sqsClient

    @Shared
    WireMockServer wireMockCore

    @DynamicPropertySource
    static void applicationProperties(DynamicPropertyRegistry registry) {
        sql = CoreAdhocHelper.initAdhoc(registry)
    }

    def setupSpec() {
        // SQSのQueueを作成しないと、起動時にエラーになる。
        objectMapper = new ObjectMapper()
        wireMockCore = new WireMockServer(options().port(8080))
        wireMockCore.start()
    }

    def cleanupSpec() {
        CoreAdhocHelper.cleanupSpec()
    }

    def setup() {
    }

    def cleanup() {
        // Do nothing.
    }

    /**
     * InvokeCoreDischargeイベントのindexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return InvokeCoreDischargeイベントのindexValuesの値
     */
    private static JsonNode getInvokeCoreDischargeIndexValues() {
        String indexValues = """
            {
                "validatorId":[56,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]
            }
        """
        return objectMapper.readTree(indexValues)
    }

    /**
     * InvokeCoreDischargeイベントのnonIndexValuesの値を、JsonNodeオブジェクトとして返します。
     * @return InvokeCoreDischargeイベントのnonIndexValuesの値
     */
    private static JsonNode getInvokeCoreDischargeNonIndexValues() {
        // accountId:6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi
        // traceId:traceid
        String nonIndexValues = """
            {
                "accountId":[54,48,48,49,111,85,122,82,105,54,109,104,57,119,109,67,50,74,118,72,72,117,54,104,49,85,54,82,48,85,104,105],
                "fromZoneId":"3001",
                "amount":"1000",
                "traceId":[116,114,97,99,101,105,100]
            }
        """
        return objectMapper.readTree(nonIndexValues)
    }

    def "1-1_testAcceptable_DischargeRequestedイベントが発生,ValidatorIdがClientEntityテーブルに存在する場合（正常系）"() {
        setup:
        JsonNode indexValues = getInvokeCoreDischargeIndexValues()
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        when:
        def result = this.invokeCoreTracker.acceptable(testEvent)

        then:
        !Objects.isNull(result)
        result.eventType() == BCEventType.DISCHARGE_REQUESTED
        result.validatorIds().size() == 1
        result.validatorIds().get(0).getValue().equals("80bOvB8L3VxYSCM5QWd1WpSNenGaGFba")
        DischargeRequestedMessage resultMessage = (DischargeRequestedMessage) result
        resultMessage.accountId.getValue().equals("6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi")
        resultMessage.fromZoneId.getValue().equals(3001)
        resultMessage.amount.getValue().equals(BigInteger.valueOf(1000))
        resultMessage.traceId.getValue().equals("traceid")
    }

    def "1-2_testAcceptable_DischargeRequestedイベント以外（処理対象外）が発生した場合"() {
        setup:
        JsonNode indexValues = getInvokeCoreDischargeIndexValues()
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("OutOfCase")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        when:
        def result = this.invokeCoreTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "1-3_testAcceptable_DischargeRequestedイベントが発生,ValidatorIdがClientEntityテーブルに存在しない場合"() {
        setup:
        // notexists
        String indexValuesNotExists = """
            {
                "validatorId":[110,111,116,101,120,105,115,116,115]
            }
        """
        JsonNode indexValues = objectMapper.readTree(indexValuesNotExists) // Not Exists
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        when:
        def result = this.invokeCoreTracker.acceptable(testEvent)

        then:
        Objects.isNull(result)
    }

    def "2-1_testOnMessage_DischargeRequestedイベント_APIレスポンス正常"() {
        setup:
        wireMockCore.stubFor(post(urlPathEqualTo("/transactions/discharge"))
                .willReturn(
                        okJson("""
                                {
                                    "account_id": "6001oUzRi6mh9wmC2JvHHu6h1U6R0Uhi",
                                    "account_name": "ﾓｯｸｷﾞﾝｺｳｺｳｻﾞ1",
                                    "discharge_amount": 1000,
                                    "from_zone_id": "3001",
                                    "from_zone_name": "ビジネスゾーン",
                                    "to_zone_id": "3000",
                                    "to_zone_name": "フィナンシャルゾーン"
                                }
                    """)
                ))

        JsonNode indexValues = getInvokeCoreDischargeIndexValues()
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        DischargeRequestedEvent invokeCoreDischargeEvent = DischargeRequestedEvent.create(testEvent)
        DischargeRequestedMessage invokeCoreDischargeMessage = DischargeRequestedMessage.create(invokeCoreDischargeEvent)

        when:
        def result = this.invokeCoreTracker.onMessage(invokeCoreDischargeMessage)

        then:
        !Objects.isNull(result)
        result == true
    }

    def "2-2_testOnMessage_DischargeRequestedイベント_SecretsManagerにclientsecretが存在しない"() {
        setup:
        // validator2（SecretsManager登録なし）
        String indexValuesNonSecrets = """
            {
                "validatorId":[118,97,108,105,100,97,116,111,114,50]
            }
        """
        JsonNode indexValues = objectMapper.readTree(indexValuesNonSecrets)
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        DischargeRequestedEvent invokeCoreDischargeEvent = DischargeRequestedEvent.create(testEvent)
        DischargeRequestedMessage invokeCoreDischargeMessage = DischargeRequestedMessage.create(invokeCoreDischargeEvent)

        when:
        def result = this.invokeCoreTracker.onMessage(invokeCoreDischargeMessage)

        then:
        // ResourceNotFoundExceptionが発生する
        Exception e = thrown()
        e.class.equals(ResourceNotFoundException)
        e.message.equals("Credential is not found in secretManager. secretId = dcjpy/core/client_credentials/validator2")
    }

    def "2-3_testOnMessage_DischargeRequestedイベント_APIレスポンス異常_タイムアウト"() {
        setup:
        wireMockCore.stubFor(post(urlPathEqualTo("/transactions/discharge"))
                .willReturn(
                        aResponse()
                        .withStatus(200)
                        .withFixedDelay(1000*120)
                ))

        JsonNode indexValues = getInvokeCoreDischargeIndexValues()
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        DischargeRequestedEvent invokeCoreDischargeEvent = DischargeRequestedEvent.create(testEvent)
        DischargeRequestedMessage invokeCoreDischargeMessage = DischargeRequestedMessage.create(invokeCoreDischargeEvent)

        when:
        def result = this.invokeCoreTracker.onMessage(invokeCoreDischargeMessage)

        then:
        Exception e = thrown() // タイムアウトが発生する
        e.class.equals(ResourceAccessException)
        e.message.contains("I/O error on POST request for \"http://localhost:8080/transactions/discharge\"" +
                ": Read timed out")
        e.cause.class.equals(SocketTimeoutException)
        e.cause.message.contains("Read timed out")
    }

    def "2-4_testOnMessage_DischargeRequestedイベント_APIレスポンス異常_エラーコード #vResponse の場合 #vMessage が出力されること"() {
        setup:
        wireMockCore.stubFor(post(urlPathEqualTo("/transactions/discharge"))
                .willReturn(badRequest()
                        .withHeader("Content-Type", "application/json")
                        .withBody("""
                                {
                                    "message": ".....",
                                    "detail": ".....",
                                    "error_code": "${vResponse}"
                                }
                                """)
                        )
        )

        JsonNode indexValues = getInvokeCoreDischargeIndexValues()
        JsonNode nonIndexValues = getInvokeCoreDischargeNonIndexValues()

        def testEvent = BCEvent.<Message> builder()
                .name("DischargeRequested")
                .indexedValues(indexValues)
                .nonIndexedValues(nonIndexValues)
                .build()

        DischargeRequestedEvent invokeCoreDischargeEvent = DischargeRequestedEvent.create(testEvent)
        DischargeRequestedMessage invokeCoreDischargeMessage = DischargeRequestedMessage.create(invokeCoreDischargeEvent)

        when:
        def result = this.invokeCoreTracker.onMessage(invokeCoreDischargeMessage)

        then:
        Exception e = thrown() // タイムアウトが発生する
        e.class.equals(InvokeCoreApiDischargeFailureException)
        e.message.equals(vMessage)

        where:
        vResponse | vMessage
        "E0005"   | "CoreAPI Error Code [E0005] Returned. Insufficient DC Account Balance."
        "E0032"   | "CoreAPI Error Code [E0032] Returned. Invalid DC Account Status."
        "E0073"   | "CoreAPI Error Code [E0073] Returned. Exceeded Discharge Limit."
        "E0025"   | "CoreAPI Error Code [E0025] Returned. Exceeded Daily Limit."
        "E0075"   | "CoreAPI Error Code [E0075] Returned. Duplicated RequestId."
        "E9999"   | "CoreAPI Error Code [E9999] Returned." // その他
    }
}
