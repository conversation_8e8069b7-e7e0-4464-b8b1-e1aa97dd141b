package com.decurret_dcp.dcjpy.bctracker.base.adaptor;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import jakarta.annotation.PostConstruct;

import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.decurret_dcp.dcjpy.bctracker.base.config.BCTrackerBaseSqsProperty;
import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.MessageHandler;
import com.decurret_dcp.dcjpy.bctracker.base.domain.SleepUtil;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.ComponentStatus;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.BlockTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlResponse;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest;
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse;
import software.amazon.awssdk.services.sqs.model.SqsException;

@Component
@Slf4j
public class AwsSqsMessageAdaptor implements MessageHandler<Message> {

    private static final ScheduledExecutorService SCHEDULED_SERVICE = Executors.newSingleThreadScheduledExecutor();

    private final ComponentStatus status;

    private final ObjectMapper objectMapper;

    private final BCTrackerBaseSqsProperty property;

    private final SqsClient sqsClient;

    private final String queueUrl;

    private final LinkedBlockingQueue<Message> messageQueue;

    private final AtomicInteger failureCount;

    public AwsSqsMessageAdaptor(BCTrackerBaseSqsProperty property, SqsClient sqsClient, ObjectMapper objectMapper) {
        this.status = new ComponentStatus();
        this.objectMapper = objectMapper;
        this.sqsClient = sqsClient;
        this.property = property;
        this.queueUrl = initQueueUrl(sqsClient, property.queueName);
        this.messageQueue = new LinkedBlockingQueue<>();
        this.failureCount = new AtomicInteger(0);
    }

    private static String initQueueUrl(SqsClient sqsClient, String queueName) {
        GetQueueUrlRequest requestUrl = GetQueueUrlRequest.builder()
                .queueName(queueName)
                .build();
        GetQueueUrlResponse responseUrl = sqsClient.getQueueUrl(requestUrl);

        return responseUrl.queueUrl();
    }

    @PostConstruct
    public void initialize() {
        SCHEDULED_SERVICE.schedule(() -> this.decrementCounter(), 10L, TimeUnit.MINUTES);
    }

    // 失敗回数を蓄積するだけだと、何かしらの理由で稀に発生する事象が積み重なって閾値を突破する可能性があるため、
    // 一定時間ごとに失敗回数をデクリメントする。
    private void decrementCounter() {
        for (int index = 0; index < 5; index++) {
            int currentCount = this.failureCount.get();
            if (currentCount == 0) {
                return;
            }

            if (currentCount < 0) {
                if (this.failureCount.compareAndSet(currentCount, 0)) {
                    return;
                }

                SleepUtil.sleepSilently(100L);
                continue;
            }

            if (this.failureCount.compareAndSet(currentCount, currentCount - 1)) {
                return;
            }

            SleepUtil.sleepSilently(100L);
        }

        log.warn("Failed to decrement failed counter. failureCount : {}", Integer.valueOf(this.failureCount.get()));
    }

    @Override
    public boolean isActive() {
        return this.status.isRunning();
    }

    @Override
    public BCEvent<Message> poll() {
        Message message = this.messageQueue.poll();
        if (message != null) {
            return this.toEvent(message);
        }

        // 既に停止要求を受け取っている場合は、追加で SQS からメッセージを取得しない。
        if (this.status.isRunning() == false) {
            this.status.terminated();
            return null;
        }

        List<Message> messages = this.fetchMessages();
        if (messages.isEmpty()) {
            return null;
        }

        for (Message newMessage : messages) {
            boolean offered = this.messageQueue.offer(newMessage);
            if (offered == false) {
                // キューから取得できないことを確認のうえで追加しているので、発生しない想定
                log.error("Failed to queuing message. {}", newMessage);
                break;
            }
        }

        return this.poll();
    }

    private BCEvent<Message> toEvent(Message message) {
        JsonNode body;
        try {
            body = this.objectMapper.readTree(message.body());
        } catch (JsonProcessingException jsonExc) {
            log.error("Failed to parse body. {}", message, jsonExc);
            return null;
        }

        JsonNode nodeMessage = body.get("Message");

        // 値を読んだ後に、parseJsonNodeにする
        JsonNode nodeDynamodb;
        try {
            JsonNode messageNode = this.parseJsonNode(nodeMessage.textValue());
            if (messageNode == null) {
                throw new IllegalArgumentException("'Message' node is null. Received event : " + message.toString());
            }

            nodeDynamodb = messageNode.get("dynamodb");
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to parse indexedValue",jsonExc);
        }

        if (nodeDynamodb == null) {
            log.error("Failed to read body lacked 'dynamodb'. {}", body);
            return null;
        }

        JsonNode nodeImage = nodeDynamodb.get("NewImage");
        if (nodeImage == null) {
            log.error("Failed to read body lacked 'NewImage'. {}", body);
            return null;
        }

        try {
            return this.doConvertEvent(message, nodeImage);
        } catch (RuntimeException exc) {
            log.error("Failed to parse message. {}", body, exc);
            return null;
        }
    }

    BCEvent<Message> doConvertEvent(Message message, JsonNode nodeImage) {
        long logIndex = JsonNodeReader.asLong(nodeImage, "logIndex", "N");
        String logText = JsonNodeReader.asText(nodeImage, "log", "S");
        long blockTimestamp = JsonNodeReader.asLong(nodeImage, "blockTimestamp", "N");
        String name = JsonNodeReader.asText(nodeImage, "name", "S");
        String transactionHash = JsonNodeReader.asText(nodeImage, "transactionHash", "S");
        JsonNode indexedValues = this.parseIndexedValues(nodeImage);
        JsonNode nonIndexedValues = this.parseNonIndexedValues(nodeImage);

        return BCEvent.<Message>builder()
                .name(name)
                .transactionHash(new TransactionHash(transactionHash))
                .blockTimestamp(BlockTimeStamp.of(blockTimestamp))
                .logIndex(logIndex)
                .log(logText)
                .indexedValues(indexedValues)
                .nonIndexedValues(nonIndexedValues)
                .original(message)
                .build();
    }

    private JsonNode parseIndexedValues(JsonNode nodeImage) {
        String indexedValues = JsonNodeReader.asText(nodeImage, "indexedValues", "S");
        try {
            return this.parseJsonNode(indexedValues);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to parse indexedValue.", jsonExc);
        }
    }

    private JsonNode parseNonIndexedValues(JsonNode nodeImage) {
        String nonIndexedValues = JsonNodeReader.asText(nodeImage, "nonIndexedValues", "S");
        try {
            return this.parseJsonNode(nonIndexedValues);
        } catch (JsonProcessingException jsonExc) {
            throw new RuntimeException("Failed to parse nonIndexedValue.", jsonExc);
        }
    }

    private JsonNode parseJsonNode(String value) throws JsonProcessingException {
        if (StringUtils.hasText(value) == false) {
            return null;
        }

        return this.objectMapper.readTree(value);
    }

    private List<Message> fetchMessages() {
        ReceiveMessageRequest request = ReceiveMessageRequest.builder()
                .queueUrl(this.queueUrl)
                .visibilityTimeout(this.property.visibilityTimeout)
                .waitTimeSeconds(this.property.waitTimeSeconds)
                .build();

        List<Message> messages = null;
        try {
            ReceiveMessageResponse response = this.sqsClient.receiveMessage(request);
            messages = response.messages();
        } catch (SqsException sqsExc) {
            int failedCount = this.failureCount.incrementAndGet();
            if (failedCount >= 5) {
                throw new RuntimeException(
                        "Exceeded the limit count of failure to receive message from " + this.queueUrl, sqsExc
                );
            }

            log.error("Failed to receive message from {}, failureCount : {}",
                      this.queueUrl, Integer.valueOf(failedCount), sqsExc);
            SleepUtil.sleepSilently(1000L);
        }

        return (messages != null) ? messages : List.of();
    }

    @Override
    public void complete(BCEvent<Message> event) {
        this.complete(event.original());
    }

    void complete(Message message) {
        DeleteMessageRequest request = DeleteMessageRequest.builder()
                .queueUrl(this.queueUrl)
                .receiptHandle(message.receiptHandle())
                .build();
        try {
            this.sqsClient.deleteMessage(request);
        } catch (SqsException sqsExc) {
            log.error("Failed to delete message from {}. message : {}", this.queueUrl, message, sqsExc);
        }
    }

    @EventListener
    public void onCloseEvent(ContextClosedEvent event) {
        if (this.status.shutdown()) {
            log.warn("Ordered to terminating polling message from sqs : {}, remaining messages : {}",
                     this.property.queueName, Integer.valueOf(this.messageQueue.size()));

            SCHEDULED_SERVICE.shutdown();
        }
    }
}
