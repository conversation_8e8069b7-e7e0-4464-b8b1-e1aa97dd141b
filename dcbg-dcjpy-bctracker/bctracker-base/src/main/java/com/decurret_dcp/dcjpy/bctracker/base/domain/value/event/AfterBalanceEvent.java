package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.Balance;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ZoneId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
public class AfterBalanceEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ValidatorId validatorId;

    public final List<AfterBalance> fromAfterBalance;

    public final List<AfterBalance> toAfterBalance;

    @lombok.Builder(access = AccessLevel.PRIVATE)
    public static class AfterBalance{
        public final ZoneId zoneId;

        public final Balance balance;
    }

    public static AfterBalanceEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.AFTER_BALANCE) {
            return null;
        }

        String validatorId = JsonNodeReader.bytesToString(bcEvent.nonIndexedValues.get("validatorId"));
        List<AfterBalance> fromAfterBalances = parseAfterBalances(bcEvent.nonIndexedValues,"fromAfterBalance");
        List<AfterBalance> toAfterBalances = parseAfterBalances(bcEvent.nonIndexedValues,"toAfterBalance");

        return AfterBalanceEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .validatorId(ValidatorId.of(validatorId))
                .fromAfterBalance(fromAfterBalances)
                .toAfterBalance(toAfterBalances)
                .build();
    }

    private static List<AfterBalance> parseAfterBalances(JsonNode nonIndexedValues,String nodeName) {
        JsonNode forceDischarge = nonIndexedValues.get(nodeName);
        if (forceDischarge == null) {
            return List.of();
        }

        List<AfterBalance> events = new ArrayList<>();
        for (JsonNode jsonNode : forceDischarge) {
            ZoneId zoneId = ZoneId.of(jsonNode.get("zoneId").asInt());
            BigInteger balance = new BigInteger(jsonNode.get("balance").asText());
            AfterBalance afterBalance = AfterBalance.builder()
                    .zoneId(zoneId)
                    .balance(Balance.of(balance))
                    .build();

            events.add(afterBalance);
        }

        return List.copyOf(events);
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.AFTER_BALANCE;
    }
}
