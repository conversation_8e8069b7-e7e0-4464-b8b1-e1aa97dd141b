package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class RequestQuery {

    private final String queryString;

    private RequestQuery(String queryString) {
        this.queryString = queryString;
    }

    public static RequestQuery of(String queryString) {
        return new RequestQuery(queryString);
    }

    public Map<String, String> parseToMap() {
        Map<String, String> queryParams = new HashMap<>();
        if (this.queryString == null || this.queryString.isEmpty()) {
            return queryParams; // 空のマップを返す
        }
        try {
            String[] pairs = this.queryString.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8);
                String value = keyValue.length > 1 ? URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8) : "";
                queryParams.put(key, value);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to parse query string: " + e.getMessage(), e);
        }
        return queryParams;
    }

    public String getValue() {
        return this.queryString;
    }
}
