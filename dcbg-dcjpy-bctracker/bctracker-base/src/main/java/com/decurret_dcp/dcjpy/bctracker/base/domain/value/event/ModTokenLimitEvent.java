package com.decurret_dcp.dcjpy.bctracker.base.domain.value.event;

import java.util.ArrayList;
import java.util.List;

import com.decurret_dcp.dcjpy.bctracker.base.domain.JsonNodeReader;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEvent;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventType;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.BCEventTypeHolder;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AccountId;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.AppTimeStamp;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.TransactionHash;
import com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic.ValidatorId;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@ToString
@EqualsAndHashCode
@Builder(access = AccessLevel.PRIVATE)
@Slf4j
public class ModTokenLimitEvent implements BCEventTypeHolder {

    public final TransactionHash transactionHash;

    public final AppTimeStamp blockTimeStamp;

    public final ValidatorId validatorId;

    public final AccountId accountId;

    public static ModTokenLimitEvent create(BCEvent<?> bcEvent) {
        if (BCEventType.of(bcEvent.name) != BCEventType.MOD_TOKEN_LIMIT) {
            return null;
        }

        String validatorId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("validatorId"));
        String accountId = JsonNodeReader.bytesToString(bcEvent.indexedValues.get("accountId"));
        boolean changed = hasChanged(bcEvent.nonIndexedValues);
        if (changed == false) {
            return null;
        }

        // コントラクトから伝搬される ModTokenLimit イベントには limitAmounts も含まれるが、
        // 利用していないため取得していない。

        return ModTokenLimitEvent.builder()
                .transactionHash(bcEvent.transactionHash)
                .blockTimeStamp(bcEvent.blockTimestamp.appTimeStamp())
                .validatorId(ValidatorId.of(validatorId))
                .accountId(AccountId.of(accountId))
                .build();
    }

    private static boolean hasChanged(JsonNode nonIndexedValues) {

        // アカウント限度額の更新フラグ
        JsonNode limitUpdates = nonIndexedValues.get("limitUpdates");
        Boolean mint = limitUpdates.get("mint").asBoolean();
        Boolean burn = limitUpdates.get("burn").asBoolean();
        Boolean charge = limitUpdates.get("charge").asBoolean();
        Boolean discharge = limitUpdates.get("discharge").asBoolean();
        Boolean transfer = limitUpdates.get("transfer").asBoolean();
        // アカウント一日の累積限度額の更新フラグ
        JsonNode cumulative = limitUpdates.get("cumulative");
        Boolean cumulativeTotal = cumulative.get("total").asBoolean();
        Boolean cumulativeMint = cumulative.get("mint").asBoolean();
        Boolean cumulativeBurn = cumulative.get("burn").asBoolean();
        Boolean cumulativeCharge = cumulative.get("charge").asBoolean();
        Boolean cumulativeDischarge = cumulative.get("discharge").asBoolean();
        Boolean cumulativeTransfer = cumulative.get("transfer").asBoolean();

        // いずれか一つの項目がtrueであること
        return mint || burn || charge || discharge || transfer ||
                cumulativeTotal || cumulativeMint || cumulativeBurn || cumulativeCharge || cumulativeDischarge || cumulativeTransfer;
    }

    @Override
    public BCEventType eventType() {
        return BCEventType.MOD_TOKEN_LIMIT;
    }
}
