package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@JsonSerialize(using = DcBankNumberJson.Serializer.class)
@JsonDeserialize(using = DcBankNumberJson.Deserializer.class)
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class DcBankNumber {

    public final String value;

    private DcBankNumber(String value) {
        this.value = value;
    }

    public static DcBankNumber of(String dcBankNumber) {
        return new DcBankNumber(dcBankNumber);
    }

    public String getValue() {
        return this.value;
    }
}
