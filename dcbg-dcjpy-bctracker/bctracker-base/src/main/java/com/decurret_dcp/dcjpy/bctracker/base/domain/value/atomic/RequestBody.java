package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Map;
import java.util.Set;

import org.seasar.doma.Domain;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class RequestBody {

    private final String jsonString;

    private RequestBody(String jsonString) {
        this.jsonString = jsonString;
    }

    public static RequestBody of(String jsonString) {
        return new RequestBody(jsonString);
    }

    public static RequestBody of(Object jsonValue) {
        String jsonString = JsonConverter.toStringValue(jsonValue);
        return new RequestBody(jsonString);
    }

    public static RequestBody of(Object jsonValue, Set<String> sensitiveKeys) {
        String jsonString = JsonConverter.toStringValue(jsonValue);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> jsonMap = objectMapper.readValue(jsonString, new TypeReference<>() {});

            // マスク処理
            for (String key : sensitiveKeys) {
                if (jsonMap.containsKey(key)) {
                    jsonMap.put(key, "[**MASKED**]");
                }
            }

            String maskedJsonString = objectMapper.writeValueAsString(jsonMap);
            return new RequestBody(maskedJsonString);
        } catch (Exception e) {
            // エラー時は元の文字列をそのまま使用
            return new RequestBody(jsonString);
        }
    }


    public String getValue() {
        return this.jsonString;
    }

}
