package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

/**
 * IB認証手続きの種別を表す。
 */
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum IbOperationType {
    /** sign_in_id_inquiry : サインインID照会。 */
    SIGN_IN_ID_INQUIRY("sign_in_id_inquiry"),

    /** temporary_password_issuarance : 仮パスワード発行。 */
    TEMPORARY_PASSWORD_ISSUARANCE("temporary_password_issuarance");

    private static final Map<String, IbOperationType> OBJECT_MAP = Arrays.stream(IbOperationType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private IbOperationType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static IbOperationType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
