package com.decurret_dcp.dcjpy.bctracker.base.domain;

import java.security.SecureRandom;
import java.util.Random;

import org.apache.commons.lang3.RandomStringUtils;

public class SecureRandomHolder {

    private static final Random RANDOM = new SecureRandom();

    private SecureRandomHolder() {
        // Do nothing.
    }

    public static String randomAlphaNumeric(int length) {
        return RandomStringUtils.random(
                length, 0, 0, true, true, null, RANDOM
        );
    }

    public static byte[] randomBytes(int length) {
        byte[] bytes = new byte[length];
        RANDOM.nextBytes(bytes);

        return bytes;
    }
}