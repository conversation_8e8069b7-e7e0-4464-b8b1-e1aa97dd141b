package com.decurret_dcp.dcjpy.bctracker.base.domain.value;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum BCEventType {

    MINT("Mint"),

    BURN("Burn"),

    TRANSFER("Transfer"),

    ISSUE_VOUCHER("IssueVoucher"),

    REDEEM_VOUCHER("RedeemVoucher"),

    FORCE_BURN("ForceBurn"),

    /** BusinessZoneAccount登録。 */
    SYNC_BUSINESS_ZONE_STATUS("SyncBusinessZoneStatus"),

    SYNC_BUSINESS_ZONE_BALANCE("SyncBusinessZoneBalance"),

    /** アカウント限度額変更。 */
    MOD_TOKEN_LIMIT("ModTokenLimit"),

    /** CoreAPI実行:ディスチャージ。 */
    DISCHARGE_REQUESTED("DischargeRequested"),

    /** アカウント総残高登録 */
    AFTER_BALANCE("AfterBalance"),

    /** 上記以外のイベント(処理しない)。 */
    NOT_SUPPORT("NotSupport");

    private static final Map<String, BCEventType> OBJECT_MAP = Arrays.stream(BCEventType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private BCEventType(String value) {
        this.value = value;
    }

    public static BCEventType of(String type) {
        BCEventType eventType = OBJECT_MAP.get(type);
        if (eventType == null) {
            return NOT_SUPPORT;
        }

        return eventType;
    }

    public String getValue() {
        return this.value;
    }
}
