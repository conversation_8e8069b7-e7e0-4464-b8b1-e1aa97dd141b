package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import org.seasar.doma.Domain;

@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public enum AppType {

    /** dcjpy_wallet_app */
    DCJPY_WALLET_APP("dcjpy_wallet_app"),

    /** dcjpy_authenticator_app */
    DCJPY_AUTHENTICATOR_APP("dcjpy_authenticator_app");

    private static final Map<String, AppType> OBJECT_MAP = Arrays.stream(AppType.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private AppType(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static AppType of(String value) {
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
