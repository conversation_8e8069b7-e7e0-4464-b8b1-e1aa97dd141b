package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class CustomSignInId {

    private final String value;

    private CustomSignInId(String value) {
        this.value = value;
    }

    public static CustomSignInId of(String customSignInId) {
        return new CustomSignInId(customSignInId);
    }

    public String getValue() {
        return this.value;
    }
}
