package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

/**
 * 銀行ユーザおよび事業者ユーザの権限を表す。
 */
public enum ServiceUserOperationAuthority {

    /** view_accounts : アカウント 一覧照会。 */
    VIEW_ACCOUNTS("view_accounts"),

    /** view_account_detail : アカウント 詳細照会。 */
    VIEW_ACCOUNT_DETAIL("view_account_detail"),

    /** view_transactions : アカウント 取引履歴照会。 */
    VIEW_TRANSACTIONS("view_transactions"),

    /** change_account : アカウント 情報変更。 */
    CHANGE_ACCOUNT("change_account"),

    /** reset_authentication : ユーザ 認証情報リセット。 */
    RESET_AUTHENTICATION("reset_authentication"),

    /** user_suspended : ユーザ サインイン停止。 */
    USER_SUSPENDED("user_suspended"),

    /** user_activated : ユーザ サインイン停止解除。 */
    USER_ACTIVATED("user_activated"),

    /** account_frozen : アカウント 凍結。 */
    ACCOUNT_FROZEN("account_frozen"),

    /** account_activated : アカウント 凍結解除。 */
    ACCOUNT_ACTIVATED("account_activated"),

    /** account_force_burned : アカウント 強制償却。 */
    ACCOUNT_FORCE_BURNED("account_force_burned"),

    /** account_force_terminated : アカウント 強制解約。 */
    ACCOUNT_FORCE_TERMINATED("account_force_terminated"),

    /** view_information : インフォメーション 照会。 */
    VIEW_INFORMATION("view_information"),

    /** change_information : インフォメーション 編集。 */
    CHANGE_INFORMATION("change_information"),

    /** exec_contract : コントラクト実行。 */
    EXEC_CONTRACT("exec_contract"),

    /** mint : DCJPY発行。 */
    MINT("mint"),

    /** burn : DCJPY償却。 */
    BURN("burn"),

    /** mint_nft : NFT発行。 */
    MINT_NFT("mint_nft"),

    /** transfer_nft : NFT移転。 */
    TRANSFER_NFT("transfer_nft"),

    /** view_nft : NFT照合。 */
    VIEW_NFT("view_nft");

    private final String value;

    private ServiceUserOperationAuthority(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
}
