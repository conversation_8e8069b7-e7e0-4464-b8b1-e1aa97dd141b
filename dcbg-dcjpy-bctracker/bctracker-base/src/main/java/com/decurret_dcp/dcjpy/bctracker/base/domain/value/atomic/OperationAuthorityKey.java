package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import org.seasar.doma.Domain;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Domain(valueType = String.class, factoryMethod = "of", acceptNull = false)
public class OperationAuthorityKey {

    private final String authority;

    private OperationAuthorityKey(String authority) {
        this.authority = authority;
    }

    public static OperationAuthorityKey of(String authority) {
        return new OperationAuthorityKey(authority);
    }

    public static OperationAuthorityKey of(DcUserOperationAuthority authority) {
        return new OperationAuthorityKey(authority.getValue());
    }

    public static OperationAuthorityKey of(ServiceUserOperationAuthority authority) {
        return new OperationAuthorityKey(authority.getValue());
    }

    public String getValue() {
        return this.authority;
    }
}
