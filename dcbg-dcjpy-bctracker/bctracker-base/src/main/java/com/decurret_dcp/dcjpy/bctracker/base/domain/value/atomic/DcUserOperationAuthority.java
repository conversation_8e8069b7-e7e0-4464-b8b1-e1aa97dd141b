package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

/**
 * 個人ユーザおよび法人ユーザの権限を表す。
 */
public enum DcUserOperationAuthority {

    /** mint : DCJPY 発行。 */
    MINT("mint"),

    /** burn : DCJPY 償却。 */
    BURN("burn"),

    /** transfer : DCJPY 送金。 */
    TRANSFER("transfer"),

    /** charge : DCJPY チャージ。 */
    CHARGE("charge"),

    /** discharge : DCJPY ディスチャージ。 */
    DISCHARGE("discharge"),

    /** view_account : アカウント 情報照会。 */
    VIEW_ACCOUNT("view_account"),

    /** view_transaction : アカウント 取引履歴照会。 */
    VIEW_TRANSACTIONS("view_transactions"),

    /** change_account : アカウント 情報変更。 */
    CHANGE_ACCOUNT("change_account"),

    /** view_information : インフォメーション閲覧。 */
    VIEW_INFORMATION("view_information"),

    /** exec_contract : コントラクト実行。 */
    EXEC_CONTRACT("exec_contract");

    private final String value;

    private DcUserOperationAuthority(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
}
