package com.decurret_dcp.dcjpy.bctracker.base.domain.value.atomic;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作結果を表す。
 */
public enum OperationResult {

    /** success : 正常。 */
    SUCCESS("success"),

    /** processing : 処理中。 */
    PROCESSING("processing"),

    /** failed : エラー。 */
    FAILED("failed");

    private static final Map<String, OperationResult> OBJECT_MAP = Arrays.stream(OperationResult.values())
            .collect(Collectors.toMap(type -> type.getValue(), type -> type));

    private final String value;

    private OperationResult(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static OperationResult of(String value) {
        if (value == null) {
            return null;
        }
        return OBJECT_MAP.get(value.toLowerCase());
    }
}
