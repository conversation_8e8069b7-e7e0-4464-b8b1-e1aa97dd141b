# Lambda

## 概要

BCMonitoring による Events テーブル(DynamoDB)へのデータ投入をトリガーに、SNS へ検知したイベントを発行する Lambda 関数

## How to use

### 前提

- aws sam コマンドがインストール済みであること
  - [AWS SAM CLI のインストール](https://docs.aws.amazon.com/ja_jp/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html)

### ローカル環境での動作確認

- SQS エミュレータ：http://localhost:9325 （SQS の設定と Queue に登録されている件数を確認できる）

#### 1.リポジトリのルートディレクトリ配下で下記コマンドを叩き、必要なコンテナを立ち上げる

```sh
% pwd
~/<PATH_TO>/dcbg-dcf-bcmonitoring-tracker

% docker network create sam-local
% docker compose up -d
```

#### 2. app ディレクトリ配下で下記のいずれかのイベントを引数に、sam local コマンドで Lambda エミュレータを起動し処理を行う

```sh
% pwd
~/<PATH_TO>/dcbg-dcf-bcmonitoring-tracker/app

// AccountEnabledベント
% ./invoke_event.sh account-enabled

// AccountIdentifiedイベント
% ./invoke_event.sh account-identified

// AddAccountイベント
% ./invoke_event.sh add-account

// AddAccountRoleイベント
% ./invoke_event.sh add-account-role

// AddTokenイベント
% ./invoke_event.sh add-token

// AddValidatorRoleイベント
% ./invoke_event.sh add-validator-role

// Transferイベント
% ./invoke_event.sh transfer

// RedeemVoucherイベント
% ./invoke_event.sh redeem-voucher

// RedeemVouchersイベント
% ./invoke_event.sh redeem-vouchers

// IssueVoucherイベント
% ./invoke_event.sh issue-voucher

// Mintイベント
% ./invoke_event.sh mint

// Burnイベント
% ./invoke_event.sh burn

// BurnCancelイベント
% ./invoke_event.sh burn-cancel

// ModTokenLimitイベント
% ./invoke_event.sh mod-token-limit

// CumulativeResetイベント
% ./invoke_event.sh cumulative-reset

// Transferイベント（ただRecords内に含まれるRecordが同一の値）
% ./invoke_event.sh duplicate

// SetTerminatedイベント
% ./invoke_event.sh set-terminated

// ValidatorEnabledイベント
% ./invoke_event.sh validator-enabled

// 複数の送金イベント
% ./invoke_event.sh ext-mix

// 1つのトランザクション内で複数の同一ベント
% ./invoke_event.sh same-events-in-a-transaction
```

#### 3. SQS にメッセージが入っていることを確認する

- SQSからメッセージを取得する

```sh
$ aws --endpoint-url=http://localhost:4566 sqs receive-message --max-number-of-messages 10 --queue-url http://sqs.ap-northeast-1.localhost.localstack.cloud:4566/************/test-queue.fifo
```

=> レスポンス例
```json
{
    "Messages": [
        {
            "MessageId": "6d5e6ca6-ff69-4add-8cca-e4b68a2f117f",
            "ReceiptHandle": "ZGYwZjFiM2UtYTk4MC00OTQ1LWFiOWYtZjg5YzIxZmNhNzAzIGFybjphd3M6c3FzOmFwLW5vcnRoZWFzdC0xOjAwMDAwMDAwMDAwMDp0ZXN0LXF1ZXVlLmZpZm8gNmQ1ZTZjYTYtZmY2OS00YWRkLThjY2EtZTRiNjhhMmYxMTdmIDE3MTIyOTgxNTguODQ0MDk1Nw==",
            "MD5OfBody": "965ba89361dbfb5c23ea0fc0edaae267",
            "Body": "{\"Type\": \"Notification\", \"MessageId\": \"e804686a-9fd2-4391-b757-76b96cba55dc\", \"TopicArn\": \"arn:aws:sns:ap-northeast-1:************:test-topic.fifo\", \"Message\": \"{\\\"eventID\\\":\\\"9ea2f5fe8942b88f52d57d70e63e2a51\\\",\\\"eventName\\\":\\\"INSERT\\\",\\\"eventVersion\\\":\\\"1.1\\\",\\\"eventSource\\\":\\\"aws:dynamodb\\\",\\\"awsRegion\\\":\\\"ap-northeast-1\\\",\\\"dynamodb\\\":{\\\"ApproximateCreationDateTime\\\":1594874888,\\\"Keys\\\":{\\\"logIndex\\\":{\\\"N\\\":\\\"0\\\"},\\\"transactionHash\\\":{\\\"S\\\":\\\"0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46986f3144898e109b3f0de20\\\"}},\\\"NewImage\\\":{\\\"logIndex\\\":{\\\"N\\\":\\\"0\\\"},\\\"log\\\":{\\\"S\\\":\\\"{\\\\\\\"address\\\\\\\":\\\\\\\"0xeec918d74c746167564401103096d45bbd494b75\\\\\\\",\\\\\\\"topics\\\\\\\":[\\\\\\\"0
x33799229c0d6a2493d11ea73e29b33e66baacd54d6831f9d3385d6b585cafc08\\\\\\\",\\\\\\\"0x************************************************************2222\\\\\\\",\\\\\\\"0x************************************************0000000000055551\\\\\\\",\\\\\\\"0x************************************************0000000000055551\\\\\\\"],\\\\\\\"data\\\\\\\":\\\\\\\"0x************************************************0000000000044441************************************************************0064\\\\\\\",\\\\\\\"blockNumber\\\\\\\":\\\\\\\"0xf2\\\\\\\",\\\\\\\"transactionHash\\\\\\\":\\\\\\\"0xc5d2e27d885399d7df10367811c9da3af72a750c4ac8699bf47ee7f4043934f1\\\\\\\",\\\\\\\"transactionIndex\\\\\\\":\\\\\\\"0x0\\\\\\\",\\\\\\\"blockHash\\\\\\\":\\\\\\\"0x0e1345b2aab0c9d39e7e5167118403687b2bf0790f6750d3f513c062c9fa6f93\\\\\\\",\\\\\\\"logIndex\\\\\\\":\\\\\\\"0x1\\\\\\\",\\\\\\\"removed\\\\\\\":false}\\\"},\\\"blockTimestamp\\\":{\\\"N\\\":\\\"**********\\\"},\\\"name\\\":{\\\"S\\\":\\\"Transfer\\\"},\\\"transactionHash\\\":{\\\"S\\\":\\\"0x498d36a17e836c723d7c4b5e87e2fd0ce3efeac46986f3144898e109b3f0de21\\\"},\\\"indexedValues\\\":{\\\"S\\\":\\\"{\\\\\\\"regionId\\\\\\\":3001}\\\"},\\\"nonIndexedValues\\\":{\\\"S\\\":\\\"{\\\\\\\"amount\\\\\\\":100,\\\\\\\"toAccountId\\\\\\\":[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99],\\\\\\\"fromAccountId\\\\\\\":[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98],\\\\\\\"sendAccountId\\\\\\\":[54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97],\\\\\\\"isAddCumulative\\\\\\\":true}\\\"}},\\\"SequenceNumber\\\":\\\"46474900000000004188509015\\\",\\\"SizeBytes\\\":2319,\\\"StreamViewType\\\":\\\"NEW_AND
_OLD_IMAGES\\\"},\\\"eventSourceARN\\\":\\\"arn:aws:dynamodb:ap-northeast-1:************:table/tsys-vetification-Events/stream/2020-07-07T06:11:40.425\\\"}\", \"Timestamp\": \"2024-04-05T06:22:29.192Z\", \"SequenceNumber\": \"14708529952316194817\", \"UnsubscribeURL\": \"http://localhost.localstack.cloud:4566/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:ap-northeast-1:************:test-topic.fifo:a151c3f6-190b-4cd9-a910-85cf46332b2e\"}"
        }
    ]
}
```

- SQSからメッセージを削除する

```sh
$ aws --endpoint-url=http://localhost:4566 sqs delete-message --queue-url http://sqs.ap-northeast-1.localhost.localstack.cloud:4566/************/test-queue.fifo --receipt-handle ${ReceitptHandle}
```
