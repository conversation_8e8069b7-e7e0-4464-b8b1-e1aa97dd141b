{"name": "bcmonitoring-stream", "version": "1.0.0", "scripts": {"lint": "npx eslint . --ext .js,.ts", "lintfix": "npx eslint . --ext .js,.ts --fix", "build": "rm -rf dist && tsc", "test": "jest --collect-coverage"}, "files": ["dist"], "bundledDependencies": ["@aws-sdk/client-sns", "json-bigint", "pino", "pino-caller", "pino-pretty", "source-map-support"], "devDependencies": {"@types/aws-lambda": "^8.10.85", "@types/jest": "^26.0.24", "@types/node": "^14.17.33", "@types/pino": "^6.3.12", "@types/validator": "^13.6.6", "@typescript-eslint/eslint-plugin": "^3.10.1", "@typescript-eslint/parser": "^3.10.1", "aws-sdk": "^2.1524.0", "aws-sdk-client-mock": "^0.5.6", "eslint": "^7.32.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.4.1", "jest": "^26.6.3", "npm-pack-zip": "^1.3.0", "prettier": "^2.4.1", "ts-jest": "^26.5.6", "typescript": "^3.9.10"}, "dependencies": {"@aws-sdk/client-sns": "^3.478.0", "@aws-sdk/client-sqs": "^3.40.0", "@types/json-bigint": "^1.0.1", "json-bigint": "^1.0.0", "pino": "^6.13.3", "pino-caller": "^3.1.0", "pino-pretty": "^7.2.0", "source-map-support": "^0.5.20"}}