#!/bin/sh -eu

event=$1

if [ $# -ne 1 ]; then
  echo "実行するには1個の引数（[実行するイベントファイル名]）が必要です" 1>&2
  exit 1
fi

cat <<__EOT__
=======================================================
実行するイベントファイル: $event
=======================================================

__EOT__

echo "##### npm build start #####"
npm run build
echo "##### npm build end #####"
echo "##### sam build start #####"
sam build -u
echo "##### sam build end #####"
sam local invoke \
  -e ./events/"$event".json \
  --docker-network sam-local \
  --parameter-overrides \
    Env=local \
    SnsEndpoint="http://sns:4566" \
    SnsTopicArn="arn:aws:sns:ap-northeast-1:000000000000:test-topic.fifo" \