import { fetchTraceId } from "./parser";

describe("Parser", () => {
  it("Return a utf8 string value if the field of traceId exists", () => {
    const params = JSON.stringify({
      traceId: [
        51, 53, 51, 100, 97, 99, 102, 54, 55, 50, 54, 57, 51, 54, 51, 102, 99,
        55, 53, 98, 99, 100, 102, 57, 99, 56, 102, 57, 48, 53, 50, 99,
      ],
    });
    const traceId = fetchTraceId(params);

    expect(traceId).toBe("353dacf67269363fc75bcdf9c8f9052c");
  });

  it("Return a utf8 string value from 0 to 16", () => {
    const params = JSON.stringify({
      traceId: [
        48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 97, 98, 99, 100, 101, 102,
      ],
    });
    const traceId = fetchTraceId(params);

    expect(traceId).toBe("0123456789abcdef");
  });

  it("Return 'undefined' if the field of traceId does not exist", () => {
    const params = JSON.stringify({});
    const traceId = fetchTraceId(params);

    expect(traceId).toBe(undefined);
  });

  it("Return 'undefined' if the param is undefined", () => {
    const traceId = fetchTraceId(undefined);

    expect(traceId).toBe(undefined);
  });
});
