import { handler, sns } from "../src";
import { logger } from "./logger";
import { ERROR_CODE } from "./constants";

jest.mock("@aws-sdk/client-sns");

describe("SNS", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("SNS client should be called once", async () => {
    const mockSend = jest.fn().mockResolvedValue({
      MessageId: "mocked-message-id",
    });
    sns.send = mockSend;

    const event = require("../events/redeem-voucher.json");

    await handler(event);
    expect(mockSend).toHaveBeenCalled();
  });

  it("SNS client should be called twice for arguments with records have two", async () => {
    const event = require("../events/transfer.json");
    const mockSend = jest.fn().mockResolvedValue({
      MessageId: "mocked-message-id",
    });
    sns.send = mockSend;

    await handler(event);
    expect(mockSend).toHaveBeenCalledTimes(2);
  });

  it("SNS client should be rejected", async () => {
    const event = require("../events/redeem-voucher.json");
    const mockSend = jest.fn().mockRejectedValue(new Error("mocked rejection"));
    sns.send = mockSend;

    const handlerPromise = handler(event);
    await expect(handlerPromise).rejects.toThrowError("mocked rejection");
  });

  it("Output a warning if the event record is MODIFY.", async () => {
    const eventName = "MODIFY";
    const eventID = "test-id";

    const event = {
      Records: [
        {
          eventID,
          eventName,
        },
      ],
    };
    const spy = jest.spyOn(logger, "warn");

    await handler(event as any);
    expect(spy).toHaveBeenCalledWith(
      `skipped(${eventID}): record event is '${eventName}', not 'INSERT'`
    );
  });

  it("Output a warning if the event record is REMOVE.", async () => {
    const eventName = "REMOVE";
    const eventID = "test-id";

    const event = {
      Records: [
        {
          eventID,
          eventName,
        },
      ],
    };
    const spy = jest.spyOn(logger, "warn");

    await handler(event as any);
    expect(spy).toHaveBeenCalledWith(
      `skipped(${eventID}): record event is '${eventName}', not 'INSERT'`
    );
  });

  it("Output an error if the event record has no transactionHash field.", async () => {
    const event = {
      Records: [
        {
          eventID: "4cd774e30dddeb27ab929549f3a384be",
          eventName: "INSERT",
          dynamodb: {
            Keys: {
              // no transactionHash field
              logIndex: {
                N: "0",
              },
            },
            NewImage: {},
          },
        },
      ],
    };
    const spy = jest.spyOn(logger, "error");

    //Assertion
    await handler(event as any);
    expect(spy).toHaveBeenCalledWith(
      expect.anything(),
      `${ERROR_CODE.INVALID_DATA_ERR} invalid DynamoDB record. (TraceId: undefined)`
    );
  });

  it("Output an error if the event record has no logIndex field.", async () => {
    const event = {
      Records: [
        {
          eventID: "4cd774e30dddeb27ab929549f3a384be",
          eventName: "INSERT",
          dynamodb: {
            Keys: {
              // no logIndex field
              transactionHash: {
                S: "0x01",
              },
            },
            NewImage: {},
          },
        },
      ],
    };
    const spy = jest.spyOn(logger, "error");

    //Assertion
    await handler(event as any);
    expect(spy).toHaveBeenCalledWith(
      expect.anything(),
      `${ERROR_CODE.INVALID_DATA_ERR} invalid DynamoDB record. (TraceId: undefined)`
    );
  });
});
