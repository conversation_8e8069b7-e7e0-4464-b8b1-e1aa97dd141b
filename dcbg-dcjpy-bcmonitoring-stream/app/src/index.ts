import { DynamoDBStreamEvent } from "aws-lambda";
import { logger } from "./logger";
import { fetchTraceId } from "./parser";
import { ERROR_CODE } from "./constants";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";

const MESSAGE_GROUP_ID = "message-group-fixed";

export const sns = ((env: string): SNSClient => {
  if (env !== "local") {
    return new SNSClient({
      apiVersion: "2010-03-31",
    });
  } else {
    const options = {
      apiVersion: "2010-03-31",
      endpoint: process.env.SNS_ENDPOINT,
    };
    return new SNSClient(options);
  }
})(process.env.ENV ?? "");

export const handler = async (event: DynamoDBStreamEvent) => {
  logger.info("Start processing");

  for (const record of event.Records) {
    if (record.eventName !== "INSERT") {
      // 新規作成レコード以外は処理しない。'INSERT'以外は不正操作の可能性があるためWARNで出力する
      logger.warn(
        `skipped(${record.eventID}): record event is '${record.eventName}', not 'INSERT'`
      );
      continue;
    }

    const newImage = record.dynamodb?.NewImage;
    const keys = record.dynamodb?.Keys;

    const nonIndexedValues =
      newImage!.nonIndexedValues && newImage!.nonIndexedValues.S;
    const traceId = fetchTraceId(nonIndexedValues);

    let params;
    try {
      const transactionHash = keys!.transactionHash.S;
      const logIndex = keys!.logIndex.N;
      const name = newImage!.name.S;
      logger.info(
        `TransactionHash: ${transactionHash}, EventName: ${name}, LogIndex: ${logIndex}. (TraceId: ${traceId})`
      );

      params = {
        TopicArn: process.env.SNS_TOPIC_ARN,
        Message: JSON.stringify(record),
        MessageGroupId: MESSAGE_GROUP_ID,
        MessageDeduplicationId: `${transactionHash}-${logIndex}`,
      };
    } catch (e) {
      logger.error(
        // eslint-disable-next-line prettier/prettier
        (typeof e === "object" && e !== null) ? e : new Error('no error object.'),
        `${ERROR_CODE.INVALID_DATA_ERR} invalid DynamoDB record. (TraceId: ${traceId})`
      );
      continue;
    }

    try {
      const command = new PublishCommand(params);
      const response = await sns.send(command);
      logger.info(
        `Success, MessageId: ${response.MessageId}. (TraceId: ${traceId})`
      );
    } catch (e) {
      logger.error(
        // eslint-disable-next-line prettier/prettier
        (typeof e === "object" && e !== null) ? e : new Error('no error object.'),
        `${ERROR_CODE.SNS_SEND_ERR} Cant send message to SNS. (TraceId: ${traceId})`
      );
      throw e;
    }
  }
  logger.info("End processing");
};
