export const fetchTraceId = (
  nonIndexedValues: string | undefined
): string | undefined => {
  if (!nonIndexedValues) {
    return undefined;
  }
  const parsedJson = JSON.parse(nonIndexedValues);
  const traceIdArr: number[] | undefined = parsedJson.traceId;
  return (
    traceIdArr &&
    traceIdArr.reduce((char: string, num: number) => {
      return char + String.fromCharCode(num);
    }, "")
  );
};
