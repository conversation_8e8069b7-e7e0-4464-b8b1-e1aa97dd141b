name: "[<PERSON><PERSON>] Continuous Deployment"
run-name: "[${{ github.event.inputs.environment }}][${{github.ref_name}}][Lambda] Continuous Deployment"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
env:
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}

permissions:
  id-token: write
  contents: read

jobs:
  build-and-push:
    runs-on: ubuntu-22.04
    environment: ${{ github.event.inputs.environment }}

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: "20"
      - uses: actions/setup-python@v4
        with:
          python-version: 3.12.5
      - uses: aws-actions/setup-sam@v2
        with:
          version: 1.118.0

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1

      - run: |
          env="${{ github.event.inputs.environment }}"
          files=($(ls -a ./scripts/env))
          for file in "${files[@]}"; do
            if [[ "$file" == *"${env}"* ]]; then
              ./scripts/sam_deploy.sh ${file#.} 1
            fi
          done

      - name: Slack Notification on Success
        if: success()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCMonitoringStream][Lambda] BCMonitoringStream release job Success"
          SLACK_COLOR: good
          SLACK_MESSAGE: <!channel> Successfully executed the BCMonitoringStream release job.

      - name: Slack Notification on Failure
        uses: rtCamp/action-slack-notify@v2
        if: failure()
        env:
          SLACK_CHANNEL: C0285NR7MPC
          SLACK_TITLE: "[${{ github.event.inputs.environment }}][BCMonitoringStream][Lambda] BCMonitoringStream release job Failure"
          SLACK_COLOR: danger
          SLACK_MESSAGE: <!channel> The BCMonitoringStream release job failed to execute.
