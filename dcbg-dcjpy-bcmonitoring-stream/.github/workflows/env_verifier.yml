name: "env-verifier"
run-name: "[${{ github.event.inputs.environment }}][${{ github.ref_name }}]  Env Verifier"

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        required: true
        description: "AWS Profile Name"

env:
  MODE: prod
  COMPONENT: bcmonitoring-stream
  SCRIPT_BASE: .
  ENV_CONFIG_BASE: dcbg-dcjpy-bcmonitoring-stream/scripts/env

permissions:
  id-token: write
  contents: read
jobs:
  verify-env:
    runs-on: ubuntu-22.04
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: "Clean up"
        run: |
          if [ -e ${{ github.workspace }}/${{ env.COMPONENT }}-${{ github.event.inputs.environment }}*.csv ]; then
            rm -f ${{ github.workspace }}/${{ env.COMPONENT }}-${{ github.event.inputs.environment }}*.csv
          fi

      - name: "Configure Prod AWS Credentials [${{ github.event.inputs.environment }}]"
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE_ARN }}
          aws-region: ap-northeast-1

      - name: "Checkout BCMonitoring-Stream"
        id: checkout-bcmonitoring-stream
        uses: actions/checkout@v4
        with:
          repository: decurret-lab/dcbg-dcjpy-bcmonitoring-stream
          ref: ${{ github.ref_name }}
          path: ./dcbg-dcjpy-bcmonitoring-stream

      - name: "Checkout dcbg-dcf-terraform-core-template (Only scripts)"
        id: checkout-terraform-core-template
        uses: actions/checkout@v4
        with:
          repository: decurret-lab/dcbg-dcf-terraform-core-template
          ref: "develop"
          path: ./dcbg-dcf-terraform-core-template
          sparse-checkout: scripts
          sparse-checkout-cone-mode: true
          token: ${{ secrets.ACCESS_TOKEN_FOR_GITOPS }}

      - name: "Run Get AWS resource data script"
        id: get-aws-core-resources
        working-directory: "./dcbg-dcf-terraform-core-template/scripts"
        run: |
          pushd "${{ github.workspace }}/${{ env.ENV_CONFIG_BASE }}"
          files=$(ls -A)
          popd
          for file in ${files[@]}; do
            if [[ "$file" != *"${{ github.event.inputs.environment }}"* ]]; then
              continue
            fi
            if [[ "$file" == *"tokyo"* ]]; then
              export AWS_DEFAULT_REGION=ap-northeast-1
            elif [[ "$file" == *"osaka"* ]]; then
              export AWS_DEFAULT_REGION=ap-northeast-3
            fi
            ./list_main_env_resources_info.sh &> ${{ github.workspace }}/"$file".csv
            echo "$file".csv
            if [ -f ${{ github.workspace }}/"$file".csv ]; then
              cat ${{ github.workspace }}/"$file".csv
            else
              echo "$file.csv not found."
            fi
          done

      - name: "Set up Python"
        id: setup-python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      - name: "Install python dependencies"
        id: install-python-dependencies
        working-directory: dcbg-dcjpy-bcmonitoring-stream/scripts/env_verifier
        run: |
          python -m pip install --upgrade pip --root-user-action=ignore
          pip install -r requirements.txt

      - name: "Run Verify Script"
        id: run-script
        working-directory: dcbg-dcjpy-bcmonitoring-stream/scripts/env_verifier
        run: |
          pushd "${{ github.workspace }}/${{ env.ENV_CONFIG_BASE }}"
          files=$(ls -A)
          popd
          for file in ${files[@]}; do
            if [[ "$file" != *"${{ github.event.inputs.environment }}"* ]]; then
              continue
            fi
            python app/env_verifier.py -c ${{ env.COMPONENT }} -env "${{ github.workspace }}/${{ env.ENV_CONFIG_BASE }}/$file" -doc ${{ github.workspace }}/"$file".csv            
          done