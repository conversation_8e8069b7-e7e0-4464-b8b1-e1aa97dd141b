# dcbg-dcjpy-bcmonitoring-stream

## Overview

BCMonitoring が検知したコントラクトの Event を Lambda で SNS に発行するアプリケーション
詳細は README.md を参照

- [Lambda](./app#readme)

### 全体の処理を把握するドキュメント

- [[基本設計]BCTracker概要設計](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2869362996/BCTracker)

### 環境変数一覧

- [環境変数一覧(BCMonitoring Web3Stream RDSStream](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2440921430/BCMonitoring+Web3Stream+RDSStream#Web3Stream)

## How to use

```shell
% cd app
% npm i
% cd -
# git hooksのパス追加
% git config --local core.hooksPath .githooks
```

- [Web3Stream, RDSStream 開発環境セットアップ手順](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2259779711/Web3Stream+RDSStream)

### deploy

- [Web3Stream デプロイ手順](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2038333447/Web3Stream)

### pre-commit

git hooks を利用して commit 時に app 配下のソースコードに linter を流す処理をしている。

## scripts

### Introduction

開発の進め方が開発者により異なると、無駄なコミュニケーションコストが発生する可能性があるため、同じ操作をする場合は同じコマンドが実行される様に下記のシェルスクリプトを使用すること

### tool-listed

| TOOLS                     | DESCRIPTION                                                                                              |
| ------------------------- | -------------------------------------------------------------------------------------------------------- |
| run-evidence-reporter.sh  | EKS修正時のエビデンス作成に必要な動作確認を一括で実行する。出力ログはそのままエビデンスシートに貼り付けられる形式となっている |
| run-test-lambda.sh        | LambdaのUTを実行する                                                                                       |
| run.sh                    | Dockerネットワーク”sam-local”を作成し、EKSおよび周辺コンポーネントであるS3,SQSをコンテナで起動する                    |
| sam_deploy.sh             | Lambdaのデプロイを手動で実行する場合に使用。詳細は[リンク先](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2038333447/Web3Stream#%E5%90%84%E7%92%B0%E5%A2%83%E3%81%B8%E3%81%AELambda%E3%81%AE%E3%83%87%E3%83%97%E3%83%AD%E3%82%A4%E6%89%8B%E9%A0%86%EF%BC%88%E6%89%8B%E5%8B%95%EF%BC%89)を参照     |
| setup.sh                  | app配下にて”npm i”を実行し必要なモジュールをインストールする                                   |

## Array と Hex の対応関係

app/lambda/events配下のjsonで使用している、idのArrayとHexの対応関係
| id              | Array                                                                                                               | Hex                                                                |
| --------------- | ------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| send_account_id | [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,97]             | 0x3630624f7642384c3356785953434d35515764315770534e656e476147466261 |
| from_account_id | [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,98]             | 0x3630624f7642384c3356785953434d35515764315770534e656e476147466262 |
| to_account_id   | [54,48,98,79,118,66,56,76,51,86,120,89,83,67,77,53,81,87,100,49,87,112,83,78,101,110,71,97,71,70,98,99]             | 0x3630624f7642384c3356785953434d35515764315770534e656e476147466263 |
| issuer_id       | [50,48,48,122,122,117,113,81,52,117,82,76,49,90,83,55,119,97,72,69,54,50,48,51,55,74,121,77,114,80,69,86]           | 0x3230307a7a7571513475524c315a53377761484536323033374a794d72504556 |
| provider_id     | [49,48,50,66,104,114,101,65,105,104,118,53,121,69,106,85,106,113,118,106,48,101,106,54,49,66,84,103,49,114,105,106] | 0x31303242687265416968763579456a556a71766a30656a36314254673172696a |
| validator_id    | [56,48,51,119,76,50,55,108,120,74,98,102,103,104,76,118,97,55,78,85,52,56,108,80,49,65,118,119,107,51,76,69]        | 0x383033774c32376c784a626667684c7661374e5534386c50314176776b334c45 |
| token_id        | [51,48,53,55,106,116,98,110,97,104,98,50,85,101,79,114,90,117,107,85,51,76,75,89,48,105,80,105,117,49,114,109]      | 0x333035376a74626e6168623255654f725a756b55334c4b59306950697531726d |
