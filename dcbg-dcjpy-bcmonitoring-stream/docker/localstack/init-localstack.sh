#!/usr/bin/env bash

# set -euo pipefail

LOCALSTACK_HOST=localhost
AWS_REGION=ap-northeast-1

echo "Start to configure LocalStack."

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  sns create-topic \
  --region ${AWS_REGION} \
  --name test-topic.fifo \
  --attributes FifoTopic=true,ContentBasedDeduplication=false

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  sqs create-queue \
  --region ${AWS_REGION} \
  --queue-name test-queue.fifo \
  --attributes FifoQueue=true

awslocal --endpoint-url=http://${LOCALSTACK_HOST}:4566 \
  sns subscribe \
  --region ${AWS_REGION} \
  --topic-arn arn:aws:sns:${AWS_REGION}:000000000000:test-topic.fifo \
  --protocol sqs \
  --notification-endpoint arn:aws:sqs:${AWS_REGION}:000000000000:test-queue.fifo

echo "Check subscriptions."
awslocal --endpoint-url=http://localhost:4566 \
  sns list-subscriptions \
  --region ${AWS_REGION}

echo "Finished configuring LocalStack."
