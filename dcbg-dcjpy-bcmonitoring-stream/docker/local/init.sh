#!/usr/bin/env bash

echo "Started uploading the lambda function."

aws configure set aws_access_key_id dummy
aws configure set aws_secret_access_key dummy

aws --endpoint-url=${LOCALSTACK_URL} \
  lambda create-function \
  --function-name bcmonitoring-stream \
  --runtime nodejs20.x \
  --zip-file fileb:///tmp/data/bcmonitoring-stream.zip \
  --handler dist/index.handler \
  --role arn:aws:iam::000000000000:role/lambda-role \
  --environment Variables="{ENV=local,SNS_TOPIC_ARN=$SNS_TOPIC_ARN,SNS_ENDPOINT=$LOCALSTACK_URL}"

STREAM_ARN=$(aws --endpoint-url=${LOCALSTACK_URL} \
  dynamodb describe-table \
  --table-name local-Events \
  --output text \
  --query 'Table.LatestStreamArn')

aws --endpoint-url=${LOCALSTACK_URL} \
  lambda create-event-source-mapping \
  --function-name bcmonitoring-stream \
  --event-source ${STREAM_ARN} \
  --batch-size 5 \
  --starting-position TRIM_HORIZON

echo "Finished uploading the lambda function."
