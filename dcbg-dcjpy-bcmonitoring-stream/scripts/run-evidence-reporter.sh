#!/bin/bash

# app配下修正時にエビデンス作成で必ず実行するコマンドを一括実行する

BASE=$(
  cd $(dirname "$0")/.. || exit
  pwd
) 2>&1 >/dev/null

# コピペの開始位置を確認しやすいよう、空行を入れておく
echo -e "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n"

echo -e "\n% pwd"
pwd

# エビデンスにそのまま貼れるよう、実行コマンドの表示
echo -e "\n% $0"

# プロジェクトルートディレクトリに移動
cd $BASE || exit

# テスト実施環境の情報を表示
echo -e "\n# exec date"
date
echo -e "\n# project directory"
pwd
echo -e "\n# current branch"
git rev-parse --abbrev-ref HEAD

# Docker起動
cd - > /dev/null || exit
echo -e "\n\n# launch Docker"
docker compose up -d

# Transferイベント情報を渡してLambda関数を起動
cd app || exit
echo -e "\n\n# transferイベント送出"
sh invoke_event.sh transfer_sync-transfer

cd - > /dev/null || exit
