#!/bin/bash -e

usage_exit() {
  echo "Usage: $0 [-d] [-t] ENV_NAME CONFIRM. -d or -t option for dry run" 1>&2
  echo "If CONFIRM is empty, AWS SAM CLI prompts to confirm whether to deploy the calculated changeset." 1>&2
  exit 1
}

DRYRUN=0
while getopts "dt" opt; do
  case "$opt" in
  d)
    DRYRUN=1
    ;;
  t)
    DRYRUN=1
    ;;
  \?)
    usage_exit
    ;;
  esac
done

shift $((OPTIND - 1))

# デプロイ先環境名
ENV=$1
if [[ -z "$ENV" ]]; then
  echo "ENV_NAME is not specified." >&2
  usage_exit
fi

# $2が空の場合デプロイ時に”Deploy this changeset? [y/N]”を出力する
CONFIRM=$2
if [[ -z "$CONFIRM" ]]; then
  CHANGESET="--confirm-changeset"
fi

SCRIPTDIR=$(
  cd $(dirname $BASH_SOURCE)
  pwd
)
ROOTDIR=$(
  cd $(dirname $BASH_SOURCE)/../app
  pwd
)

# 環境別変数の読み込み
source ${SCRIPTDIR}/env/.${ENV}

# テスト
cd $ROOTDIR
npm install && npm run test
# ビルド
npm run build && sam build -u

if [[ $DRYRUN == 1 ]]; then
  DRYRUN_OPTION="--no-execute-changeset"
fi

sam deploy "$DRYRUN_OPTION" $CHANGESET \
  --resolve-s3 \
  --stack-name dcbg-dcjpy-bcmonitoring-stream \
  --tags Environment="$ENV" \
  --capabilities CAPABILITY_IAM \
  --region "$AWS_REGION" \
  --parameter-overrides \
  Env=$ENV \
  SourceStream="$SOURCE_STREAM" \
  SnsTopicArn=$SNS_TOPIC_ARN
