ARG PYTHON_VER=3.12.4

# Dev Stage
FROM python:${PYTHON_VER}-slim-bookworm AS developer

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

WORKDIR /opt/verifier

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    wget curl \
    && apt-get -y clean \
    && rm -rf /var/lib/apt/lists/*

# helm
RUN curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["/usr/bin/tail", "-F", "/dev/null"]
