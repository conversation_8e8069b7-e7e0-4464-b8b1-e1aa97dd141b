import os
import errno
import argparse
import yaml
import ujson
import pandas as pd
import re
from pathlib import Path


def get_target_config(component):
    """Return target configuration file name from base configuration and target configuration file.

    """
    try:
        config_base = os.path.join(os.environ['SCRIPT_BASE'], "config")
        config_file = "{0}.yaml".format(os.path.join(config_base, component))
        if not os.path.isfile(config_file):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), config_file)

        with open(config_file, encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
        return yaml_config

    except Exception as e:
        raise


def get_aws_resource_raw_data(aws_file_name, target_yaml):
    """Get items that match the YAML definition from the AWS resource raw data.

    """
    try:
        aws_doc_base = "."
        aws_file = os.path.join(aws_doc_base, aws_file_name)
        if not os.path.isfile(aws_file):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), aws_file)

        aws_resources_df = pd.read_csv(aws_file, names=['module', 'column', 'value', 'note'])

        match_dict = {}
        for key, values in target_yaml["env_define_map"].items():
            match_values = []

            doc_define_names = values.get("doc_define_name")
            doc_action = values.get("doc_action")
            doc_format = values.get("doc_format")

            for doc_define_name in doc_define_names:

                match_value = \
                    (aws_resources_df[aws_resources_df['column'] == doc_define_name]).to_dict(orient='records')[0][
                        'value']

                if match_value is None:
                    raise

                if ".json" not in doc_define_name:
                    if doc_action is None or 'trim' not in doc_action:
                        match_values.append(match_value)
                    else:
                        match_values.append(re.sub(str(doc_format), "", match_value))
                else:
                    json_obj = ujson.loads(match_value)
                    match_values.append(json_obj['config']['chainId'])

            match_dict[key] = str(match_values[0])

            if "join" == doc_action:
                match_dict[key] = doc_format.format(match_values)

        return match_dict

    except Exception as e:
        raise


def get_app_env_configs(target_env):
    try:
        env_config = []

        if not os.path.isfile(target_env):
            raise FileNotFoundError(errno.ENOENT, os.strerror(errno.ENOENT), target_env)

        env_file_data = Path(target_env).read_text().splitlines()

        for env_data in env_file_data:
            env_config.append([env_data.split("=")[0], env_data.split("=")[1].strip('"')])

        return env_config

    except Exception as e:
        raise


def diff_app_env_values(app_env_configs, aws_resource_data):
    for aws_key, aws_value in sorted(aws_resource_data.items()):
        for env_config in app_env_configs:

            env_key = env_config[0]
            env_value = env_config[1]

            if aws_key == env_key:
                is_match = (env_value == aws_value)
                print("{0}\t: Result -> {1}".format(env_key, "MATCH" if is_match else "UNMATCH"))
                print("\tAPP_ENV_VALUE\t: {0}".format(env_value))
                print("\tAWS_VALUE\t: {0}".format(aws_value))


def main() -> None:
    parser = argparse.ArgumentParser(description="AWS Environment variable checker")
    parser.add_argument("-c", choices=['bcmonitoring-stream'], required=True, help='Component Name')
    parser.add_argument("-env", required=True, help='Application environment file path')
    parser.add_argument("-doc", required=True, help='AWS環境情報取得スクリプトを実行した RAW データファイルのフルパス')

    args = parser.parse_args()
    target_component = args.c
    target_env = args.env
    target_doc = args.doc

    # Get Target Config
    target_config = get_target_config(target_component)

    # Get AWS resource raw data
    aws_resources = get_aws_resource_raw_data(target_doc, target_config)

    # Get .env Values
    app_env_configs = get_app_env_configs(target_env)

    diff_app_env_values(app_env_configs, aws_resources)


if __name__ == '__main__':
    main()
