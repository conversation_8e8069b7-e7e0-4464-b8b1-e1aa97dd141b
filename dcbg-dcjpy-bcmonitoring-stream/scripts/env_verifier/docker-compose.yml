services:

  k8s-env-verifier:
    container_name: verifier
    build:
      context: .
      target: developer
      dockerfile: ./docker/Dockerfile
      args:
        PYTHON_VER: 3.12.4
    environment:
      - SCRIPT_BASE=/opt/verifier
      - ENV_CONFIG_BASE=/opt/env_data
      - AWS_DEFAULT_REGION=ap-northeast-1
    volumes:
      - .:/opt/verifier
      - ../env:/opt/env_data
    tty: true
    security_opt:
      - apparmor:unconfined
    cap_add:
      - SYS_PTRACE
