import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class TestHexParsing {
    public static void main(String[] args) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        
        // Test case 1: DynamoDB Number type with hex string value
        String dynamoDbHexData = """
        {
            "logIndex": {
                "N": "0x2"
            }
        }
        """;
        
        // Test case 2: DynamoDB Number type with decimal string value  
        String dynamoDbDecimalData = """
        {
            "logIndex": {
                "N": "2"
            }
        }
        """;
        
        JsonNode hexNode = mapper.readTree(dynamoDbHexData);
        JsonNode decimalNode = mapper.readTree(dynamoDbDecimalData);
        
        JsonNode hexLogIndexNode = hexNode.get("logIndex").get("N");
        JsonNode decimalLogIndexNode = decimalNode.get("logIndex").get("N");
        
        System.out.println("Hex string '0x2' as JsonNode:");
        System.out.println("  - asText(): " + hexLogIndexNode.asText());
        System.out.println("  - asLong(): " + hexLogIndexNode.asLong());
        System.out.println("  - isTextual(): " + hexLogIndexNode.isTextual());
        System.out.println("  - isNumber(): " + hexLogIndexNode.isNumber());
        
        System.out.println("\nDecimal string '2' as JsonNode:");
        System.out.println("  - asText(): " + decimalLogIndexNode.asText());
        System.out.println("  - asLong(): " + decimalLogIndexNode.asLong());
        System.out.println("  - isTextual(): " + decimalLogIndexNode.isTextual());
        System.out.println("  - isNumber(): " + decimalLogIndexNode.isNumber());
    }
}
